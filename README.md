# SailFusion Cloud 帆一异构智算云平台 Web 应用

这是一个帆一异构智算云平台的 Web 应用。此项目已从纯前端原型发展为前后端分离的应用，大部分核心功能已对接真实的后端接口。应用旨在完整地展示核心用户流程，包括用户租用实例、提供方托管实例以及管理员进行后台管理。重点在于 UI/UX 的流畅性和交互的真实感。（类似 Vast.ai、RunPod 等）

## 产品逻辑

### 用户

1. 用户注册（普通用户）
2. 租用实例（算力市场）
   1. 可以根据各类参数筛选实例
   2. 可以根据应用筛选实例（应用对机器有最低闲置，这种筛选下，订购完成后，询问用户是否自动安装该应用）
   3. AI 推荐实例（通过引导+多轮对话明确用户需求，然后展示推荐的实例信息，用户可以直接订购）
   4. 订购时，可以定制设备配置，比如 8 卡设备，用户可选 1 ～ 8 卡，硬盘同理，然后生成实例。
      1. 选择 2 卡后，下次该设备只可选择 6 卡
3. 实例管理（我的实例）
   1. 展示实例的详细配置、运行状态等。
   2. 查看资源使用监控（含计费信息）
   3. 应用：应用面板（如访问地址等信息）、安装新应用（替换当前镜像 / 增量安装，细节待定）
   4. 操作：SSH 连接、退订
4. 应用管理（应用市场）
   1. 卡片展示，可以根据分类（如开发、绘图、工业应用等）、名称、标签（DeepSeek 等特点）进行筛选
   2. 支持一键部署，选择现有实例、托管设备（未上架的），或过滤可购买的实例（注意，实例可能不满足应用需求，需要有相关提示）
   3. 个人模板（可以先不做，后续可以从用户当前的实例镜像直接生成）
5. 个人相关
   1. 账单
   2. 团队（低优先级）。如果用户没有升级为团队，可以点击升级为团队（付费点）。升级为团队后，可以添加团队成员（可以邀请用户加入），分配可操作实例，转让管理员权限。
   3. 个人设置
6. 设备托管（查询到用户有托管的设备后，按钮可以换一个名字，比如“我的设备”）
   1. 可以通过 SSH （自动）/ 脚本（手动，提供操作文档）接入设备
      1. 定价。提供 AI 辅助定价功能
   2. 可以查看所有的设备。设备状态分为：未上架（自用）、已上架（出租）。租售中（不同的概念）
   3. 租售/下架，可以切换设备状态
7. 多语言切换（简体中文、繁体中文、英文、泰文。暂不重要，不做）

### 超管

1. 设备管理
   1. 统一的列表，展示各类指标、资源占用情况、计费收益等。可以查看设备下的实例状态等（一个设备可以拆分为多个实例）
   2. 设备集群接入，按钮。（相当于企业级的设备托管，接入后会分配企业租户，然后把对应的企业账号给到企业。企业登录后，可以进行出租等常规流程）
   3. 操作项：
      1. 设备：强制下架；分配租户
      2. 实例：SSH 连接（异常维护）、查看日志
2. 应用模板管理
   1. 展示所有应用模板，类似上面的应用管理功能，但是新增超管相关功能。如上架、下架、价格配置、新增、编辑、删除等
   2. 前期做四个，开发环境、裸模型、开源应用（Open WebUI）、帆一应用（Halo 可视化）
3. 分润管理
   1. 列表展示所有分润规则
   2. 分润规则可以应用在全局、团队、实例、应用，优先级逐步提高，以实例的分润规则为准（前期可以固定全局分润）。实例具体的分润从实例、团队、全局逐级获取。

## 技术栈

1. React + Javascript, 部分类型定义及工具函数使用 Typescript
2. Tailwind CSS
3. Vite
4. Shadcn UI
5. Radix UI / Ant Design
6. ahooks + axios

## 当前实现状态

### 核心功能模块

1. **用户功能**
   - ✅ 用户注册/登录
   - ✅ 算力市场：浏览和租用计算资源，支持多种筛选条件
   - ✅ 我的实例：管理已租用的计算实例，查看资源使用监控和计费信息
   - ✅ 应用市场：浏览和部署应用，支持一键部署到现有实例
   - ✅ 个人设置：用户个人信息管理
   - ✅ 设备托管：用户可以托管自己的设备并出租

2. **超管功能**
   - ✅ 设备管理：管理所有设备，查看设备指标和资源占用情况
   - ✅ 应用模板管理：管理应用模板，支持上架、下架、价格配置等操作
   - ✅ 分润管理：管理分润规则

### 接口对接状态

大部分核心功能已对接真实后端接口，主要接口包括：

- GPU 枚举：`GET /ihmp/gpu`
- CPU 枚举：`GET /ihmp/cpu`
- 区域枚举：`GET /ihmp/region`
- 算力市场列表：`POST /ihmp/market/page`
- 实例管理：`POST /ihmp/instance/page` 等
- 应用模板管理：`GET /ihmp/api/app-template/page` 等

### 技术特点

- 使用 React + JavaScript/TypeScript 构建
- 使用 ahooks 的 useRequest 进行接口请求管理
- 集成错误处理和加载状态显示
- 响应式设计，支持移动端
- 使用 Ant Design 和 Shadcn UI 组件库
- 使用 Tailwind CSS 进行样式设计
