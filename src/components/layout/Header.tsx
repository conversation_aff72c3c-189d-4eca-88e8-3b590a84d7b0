import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { useNavigate, NavLink } from 'react-router-dom';
import { Dropdown, Avatar, type MenuProps } from 'antd';
import {
  Server,
  User,
  LogOut,
  Settings,
  HardDrive,
  ChevronDown,
  LayoutDashboard,
  ShoppingCart,
  Store,
  Shield
} from 'lucide-react';
import { cn } from '@/lib/utils';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleMenuClick = (path: string) => {
    navigate(path);
  };

  // Ant Design 下拉菜单配置
  const menuItems: MenuProps['items'] = [
    {
      key: 'profile',
      label: (
        <div className="flex items-center gap-3 py-1">
          <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <Settings className="w-4 h-4 text-blue-600" />
          </div>
          <span>个人中心</span>
        </div>
      ),
      onClick: () => handleMenuClick('/profile'),
    },
    {
      key: 'device-hosting',
      label: (
        <div className="flex items-center gap-3 py-1">
          <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <HardDrive className="w-4 h-4 text-green-600" />
          </div>
          <span>托管设备</span>
        </div>
      ),
      onClick: () => handleMenuClick('/device-hosting'),
    },
    ...(user?.role === 'admin' || user?.role === 'super_admin' ? [{
      key: 'super-admin',
      label: (
        <div className="flex items-center gap-3 py-1">
          <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <Shield className="w-4 h-4 text-purple-600" />
          </div>
          <span>超管后台</span>
        </div>
      ),
      onClick: () => handleMenuClick('/super-admin'),
    }] : []),
    {
      type: 'divider',
    },
    {
      key: 'logout',
      label: (
        <div className="flex items-center gap-3 py-1 text-red-600">
          <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
            <LogOut className="w-4 h-4 text-red-600" />
          </div>
          <span>退出登录</span>
        </div>
      ),
      onClick: () => logout(),
    },
  ];

  const navItems = [
    {
      name: '控制面板',
      href: '/',
      icon: LayoutDashboard,
    },
    {
      name: '算力市场',
      href: '/compute-market',
      icon: ShoppingCart,
    },
    {
      name: '我的实例',
      href: '/my-instances',
      icon: Server,
    },
    {
      name: '应用市场',
      href: '/app-market',
      icon: Store,
    },
  ];

  return (
    <header className="bg-white shadow-lg border-b border-blue-200/50 sticky top-0 z-40 backdrop-blur-sm">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left side - Logo */}
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3">
              <div className="relative">
                <div className="w-12 h-12 bg-blue-600 rounded-2xl flex items-center justify-center shadow-md">
                  <Server className="w-7 h-7 text-white" />
                </div>

              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
                  SailFusion Cloud
                </h1>
                <p className="text-sm text-gray-600">帆一异构智算云平台</p>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="hidden md:flex items-center space-x-2">
              {navItems.map((item) => (
                <NavLink
                  key={item.href}
                  to={item.href}
                  className={({ isActive }) =>
                    cn(
                      'relative flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 group',
                      isActive
                        ? 'bg-blue-600 text-white shadow-md backdrop-blur-sm'
                        : 'text-gray-600 hover:bg-blue-50 hover:text-blue-600 hover:shadow-md'
                    )
                  }
                >
                  {({ isActive }) => (
                    <>
                      <item.icon className={cn(
                        "w-4 h-4 flex-shrink-0 transition-transform duration-300",
                        isActive ? "scale-110" : "group-hover:scale-105"
                      )} />
                      <span>{item.name}</span>
                      {isActive && (
                        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-white rounded-full"></div>
                      )}
                    </>
                  )}
                </NavLink>
              ))}
            </nav>
          </div>

          {/* User Menu */}
          <Dropdown
            menu={{
              items: menuItems,
              style: {
                background: 'rgba(255, 255, 255, 0.95)',
                backdropFilter: 'blur(12px)',
                borderRadius: '16px',
                minWidth: '224px',
                overflow: 'hidden'
              }
            }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div
              className="flex items-center space-x-3 cursor-pointer rounded-xl backdrop-blur-sm transition-all duration-300"
            >
              <Avatar
                size={36}
                gap={6}
                className="bg-blue-600 text-white flex items-center justify-center shadow-md"
              >
                {user?.username}
              </Avatar>
              <ChevronDown className="w-4 h-4 text-gray-600 transition-transform duration-300" />
            </div>
          </Dropdown>
        </div>
      </div>

      {/* Mobile Navigation Menu */}
      <div className="md:hidden border-t border-blue-200/50 bg-blue-50/80 backdrop-blur-sm">
        <nav className="px-6 py-3 flex items-center space-x-2 overflow-x-auto">
          {navItems.map((item) => (
            <NavLink
              key={item.href}
              to={item.href}
              className={({ isActive }) =>
                cn(
                  'flex items-center space-x-2 px-4 py-2.5 rounded-xl text-sm font-medium transition-all duration-300 whitespace-nowrap',
                  isActive
                    ? 'bg-blue-600 text-white shadow-md backdrop-blur-sm'
                    : 'text-gray-600 hover:bg-blue-100 hover:text-blue-600'
                )
              }
            >
              <item.icon className="w-4 h-4 flex-shrink-0" />
              <span>{item.name}</span>
            </NavLink>
          ))}
        </nav>
      </div>


    </header>
  );
};

export default Header;
