import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate } from 'react-router-dom';
import Header from './Header';
import FeedbackButton from '@/components/FeedbackButton';
import ScrollToTop from '@/components/ScrollToTop';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { user, isLoading } = useAuth();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!user) {
    return <Navigate to="/login" replace />;
  }

  return (
    <div className="h-screen bg-white flex flex-col overflow-hidden">
      {/* 路由切换时滚动到顶部 */}
      <ScrollToTop scrollContainerSelector="main.flex-1" />

      {/* 固定Header */}
      <Header />

      {/* 内容区域 - 可滚动 */}
      <main className="flex-1 overflow-y-auto bg-gray-50">
        <div className="p-4">
          <div className="max-w-full mx-auto px-2">
            {children}
          </div>
        </div>
      </main>
      {/* 反馈按钮 */}
      <FeedbackButton />
    </div>
  );
};

export default Layout;
