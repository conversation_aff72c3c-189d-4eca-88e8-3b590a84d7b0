import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { Menu } from 'antd';
import {
  Server,
  Store,
  DollarSign,
  BarChart3,
  Settings,
  ArrowLeft,
  Database,
  Users,
  Shield
} from 'lucide-react';
import ScrollToTop from '@/components/ScrollToTop';

interface SuperAdminLayoutProps {
  children: React.ReactNode;
}

const SuperAdminLayout: React.FC<SuperAdminLayoutProps> = ({ children }) => {
  const { user, isLoading } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // 权限检查：只有管理员和超管可以访问
  if (!user || (user.role !== 'admin' && user.role !== 'super_admin')) {
    return <Navigate to="/" replace />;
  }

  const menuItems = [
    {
      key: '/super-admin',
      icon: <BarChart3 className="w-4 h-4" />,
      label: '概览',
    },
    {
      key: '/super-admin/devices',
      icon: <Server className="w-4 h-4" />,
      label: '设备管理',
    },
    {
      key: '/super-admin/app-templates',
      icon: <Store className="w-4 h-4" />,
      label: '应用模板管理',
    },
    {
      key: '/super-admin/billing',
      icon: <Database className="w-4 h-4" />,
      label: '账单管理',
    },
    {
      key: '/super-admin/profit-sharing',
      icon: <DollarSign className="w-4 h-4" />,
      label: '分润管理',
    },
    {
      key: '/super-admin/users',
      icon: <Users className="w-4 h-4" />,
      label: '用户管理',
    },
    {
      key: '/super-admin/system',
      icon: <Settings className="w-4 h-4" />,
      label: '系统设置',
    },
  ];

  const handleMenuClick = (e: any) => {
    navigate(e.key);
  };

  const handleBackToMain = () => {
    navigate('/');
  };

  return (
    <div className="h-screen bg-white flex overflow-hidden">
      {/* 路由切换时滚动到顶部 */}
      <ScrollToTop scrollContainerSelector="main.flex-1" />

      {/* 左侧菜单 - 占据全高度 */}
      <div className="w-64 bg-gradient-to-b from-blue-600 to-blue-700 shadow-lg border-r border-blue-500/30 flex flex-col">
        {/* 头部 */}
        <div className="p-4 border-b border-blue-500/30 flex-shrink-0">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-white rounded-xl flex items-center justify-center shadow-md">
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-lg font-bold text-white">超管后台</h1>
              <p className="text-sm text-blue-100">管理员控制面板</p>
            </div>
          </div>

          <button
            onClick={handleBackToMain}
            className="flex items-center space-x-2 text-sm text-blue-100 hover:text-white transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>返回主界面</span>
          </button>
        </div>

        {/* 菜单 - 可滚动 */}
        <div className="flex-1 p-2">
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            className="!border-none border-transparent h-full overflow-y-auto !bg-transparent"
            theme="dark"
            style={{
              backgroundColor: 'transparent',
            }}
          />
        </div>
      </div>

      {/* 主内容区域 - 内部滚动 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <main className="flex-1 overflow-y-auto p-4">
          <div className="max-w-full mx-auto px-2">
            {children}
          </div>
        </main>
      </div>
    </div>
  );
};

export default SuperAdminLayout;
