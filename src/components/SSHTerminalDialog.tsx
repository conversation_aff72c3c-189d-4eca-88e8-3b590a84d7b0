import { useEffect, useRef, useState, forwardRef, useImperativeHandle } from 'react';
import { Modal } from 'antd';
import { Terminal as XtermTerminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { AttachAddon } from 'xterm-addon-attach';
import 'xterm/css/xterm.css';
import { toast } from 'sonner';

interface InstanceInfo {
  id: string;
  name: string;
}

export interface SSHTerminalDialogRef {
  open: (instanceInfo: InstanceInfo) => void;
}

const SSHTerminalDialog = forwardRef<SSHTerminalDialogRef, {}>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [instanceInfo, setInstanceInfo] = useState<InstanceInfo | null>(null);

  const terminalRef = useRef<HTMLDivElement>(null);
  const xtermRef = useRef<XtermTerminal | null>(null);
  const fitAddonRef = useRef<FitAddon | null>(null);
  const socketRef = useRef<WebSocket | null>(null);

  useImperativeHandle(ref, () => ({
    open: (info) => {
      setInstanceInfo(info);
      setIsOpen(true);
    },
  }));

  const handleClose = () => {
    setIsOpen(false);
    setInstanceInfo(null);
    if (socketRef.current) {
      socketRef.current.close();
      socketRef.current = null;
    }
    if (xtermRef.current) {
      xtermRef.current.dispose();
      xtermRef.current = null;
    }
  };

  useEffect(() => {
    if (isOpen && instanceInfo && terminalRef.current) {
      // 清理之前的实例
      if (xtermRef.current) {
        xtermRef.current.dispose();
      }
      if (socketRef.current) {
        socketRef.current.close();
      }

      const xterm = new XtermTerminal({
        cursorBlink: true,
        fontFamily: 'Menlo, "DejaVu Sans Mono", Consolas, "Lucida Console", monospace',
        fontSize: 14,
        theme: {
          background: '#1a1a1a',
          foreground: '#f0f0f0',
          cursor: '#f0f0f0',
        },
      });
      xtermRef.current = xterm;

      const fitAddon = new FitAddon();
      fitAddonRef.current = fitAddon;
      xterm.loadAddon(fitAddon);
      xterm.open(terminalRef.current);
      fitAddon.fit();

      xterm.writeln(`> 正在连接 ${instanceInfo.name}...`);

      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const wsUrl = `${protocol}//${host}/basic-api/ihmp/app/terminal/${instanceInfo.id}`;

      const socket = new WebSocket(wsUrl);
      socketRef.current = socket;

      socket.onopen = () => {
        xterm.writeln('\x1b[32m✔ 连接建立成功!\x1b[0m');
        const attachAddon = new AttachAddon(socket);
        xterm.loadAddon(attachAddon);
      };

      socket.onerror = (event) => {
        console.error('WebSocket error:', event);
        xterm.writeln('\x1b[31m❌ 连接失败.\x1b[0m');
        toast.error('连接到SSH终端失败。');
      };

      socket.onclose = (event) => {
        xterm.writeln(`\x1b[33m连接关闭。代码：${event.code}，原因：${event.reason || '未提供原因'}\x1b[0m`);
      };

      const resizeObserver = new ResizeObserver(() => {
        fitAddon.fit();
      });
      if (terminalRef.current) {
        resizeObserver.observe(terminalRef.current);
      }

      return () => {
        resizeObserver.disconnect();
        socket.close();
        xterm.dispose();
      };
    }
  }, [isOpen, instanceInfo]);

  return (
    <Modal
      title={`SSH 终端 - ${instanceInfo?.name || ''}`}
      open={isOpen}
      maskClosable={false}
      onCancel={handleClose}
      width="80vw"
      style={{ top: 20 }}
      styles={{
        body: {
          height: '70vh',
          background: '#1a1a1a',
          padding: '10px',
          marginTop: '10px',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '8px',
          overflow: 'auto'
        },
      }}
      footer={[
        <button key="close" onClick={handleClose} className="ant-btn ant-btn-primary">
          关闭
        </button>,
      ]}
    >
      <div ref={terminalRef} style={{ width: '100%', height: '100%' }} />
    </Modal>
  );
});

export default SSHTerminalDialog;

