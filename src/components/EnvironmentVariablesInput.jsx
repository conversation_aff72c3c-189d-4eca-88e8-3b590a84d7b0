import React, { useEffect } from 'react';
import { Form, Input, Button, Row, Col, Card, Typography } from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * 环境变量配置组件
 * 支持动态添加/删除环境变量键值对
 * 支持查看模式和编辑模式
 * 支持预填充数据
 */
const EnvironmentVariablesInput = ({
  disabled = false,
  isView = false, // 查看模式，优先级高于 disabled
  title = '环境变量配置',
  showTitle = true,
  size = 'default',
  fieldName = 'environmentVariables', // Form.List 的字段名
  placeholder = {
    key: '如: DATABASE_URL',
    value: '如: mysql://localhost:3306/db'
  },
  helpText = '💡 提示：环境变量将在应用启动时注入到容器中，请确保变量名和值的正确性。'
}) => {
  // 实际的禁用状态
  const isDisabled = isView || disabled;
  const getValueRules = () => {
    return [
      { required: true, message: '请输入变量值' }
    ];
  };

  return (
    <Card
      title={showTitle ? title : null}
      size={size}
      className={showTitle ? '' : 'border-0 shadow-none'}
      styles={{ body: showTitle ? {} : { padding: 0 } }}
    >
      <Form.List name={fieldName}>
        {(fields, { add, remove }) => (
          <>
            {fields.length === 0 && (
              <div className="text-center py-4 text-gray-500">
                <Text type="secondary">暂无环境变量配置</Text>
              </div>
            )}

            {fields.map(({ key, name, ...restField }, index) => (
              <Row key={key} gutter={16} align="middle" >
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, 'key']}
                    label={index === 0 ? '变量名' : ''}
                    rules={[
                      { required: true, message: '请输入变量名' },
                      {
                        pattern: /^[A-Z_][A-Z0-9_]*$/,
                        message: '变量名只能包含大写字母、数字和下划线，且以字母或下划线开头'
                      }
                    ]}
                  >
                    <Input
                      placeholder={placeholder.key}
                      disabled={isDisabled}
                    />
                  </Form.Item>
                </Col>
                <Col span={10}>
                  <Form.Item
                    {...restField}
                    name={[name, 'value']}
                    label={index === 0 ? '变量值' : ''}
                    rules={getValueRules()}
                  >
                    <Input
                      placeholder={placeholder.value}
                      disabled={isDisabled}

                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  {!isDisabled && (
                    <Form.Item
                      {...restField}
                      label={index === 0 ? '操作' : ''}
                    >
                      <Button
                        type="text"
                        danger
                        icon={<MinusCircleOutlined />}
                        onClick={() => remove(name)}
                        className="flex items-center justify-center"
                      />
                    </Form.Item>
                  )}
                </Col>
              </Row>
            ))}

            {!isDisabled && (
              <Form.Item className="mb-0">
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  添加环境变量
                </Button>
              </Form.Item>
            )}

            {/* 提示信息 */}
            {fields.length > 0 && helpText && (
              <div className="mt-6 p-3 bg-blue-50 rounded-lg">
                <Text type="secondary" className="text-xs">
                  {helpText}
                </Text>
              </div>
            )}
          </>
        )}
      </Form.List>
    </Card>
  );
};

export default EnvironmentVariablesInput;
