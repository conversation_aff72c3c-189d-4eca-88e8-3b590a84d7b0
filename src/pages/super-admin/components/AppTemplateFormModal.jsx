import React, { useState, useEffect } from 'react';
import {
  Modal,
  Tabs,
  Button,
  Form,
  message,
  Input,
  Select,
  Switch,
  Row,
  Col,
  Card,
  Divider,
  Upload,
} from 'antd';
import {
  PlusOutlined,
  MinusCircleOutlined,
  EditOutlined,
} from '@ant-design/icons';
import {
  appTemplateService,
  APP_TYPE_LABELS,
} from '@/services/appTemplate';
import { toast } from 'sonner';
import http from '@/utils/http';
import { getOssUrl } from '@/lib/utils';
import { Spin } from 'antd';
import EnvironmentVariablesInput from '@/components/EnvironmentVariablesInput';

const { TextArea } = Input;
const { Option } = Select;

const AppTemplateFormModal = ({
  visible,
  type,
  record,
  onSuccess,
  onCancel,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [loading, setLoading] = useState(false);
  const [iconPreview, setIconPreview] = useState('');
  const [iconUploadLoading, setIconUploadLoading] = useState(false);


  const isView = type === 'view';
  const title = {
    create: '新增应用模板',
    edit: '编辑应用模板',
    view: '查看应用模板',
    copy: '复制应用模板',
  }[type];

  // 当记录变化时，设置表单值
  useEffect(() => {
    if (record && (type === 'edit' || type === 'view' || type === 'copy')) {
      // 处理协议端口数据
      let protocolPorts = [];
      if (record.protocolPortsList && Array.isArray(record.protocolPortsList)) {
        protocolPorts = record.protocolPortsList;
      } else if (record.protocolPorts) {
        try {
          protocolPorts =
            typeof record.protocolPorts === 'string'
              ? JSON.parse(record.protocolPorts)
              : record.protocolPorts;
        } catch (e) {
          console.error('解析协议端口数据失败:', e);
          protocolPorts = [{ protocol: 'http', port: '8080' }];
        }
      } else {
        protocolPorts = [{ protocol: 'http', port: '8080' }];
      }

      // 处理环境变量数据
      let environmentVariables = [];
      if (
        record.environmentVariablesList &&
        Array.isArray(record.environmentVariablesList)
      ) {
        environmentVariables = record.environmentVariablesList;
      } else if (record.environmentVariables) {
        try {
          environmentVariables =
            typeof record.environmentVariables === 'string'
              ? JSON.parse(record.environmentVariables)
              : record.environmentVariables;
        } catch (e) {
          console.error('解析环境变量数据失败:', e);
          environmentVariables = [];
        }
      }

      form.setFieldsValue({
        ...record,
        tags: record.tags
          ? record.tags
            .split(',')
            .map(tag => tag.trim())
            .join(', ')
          : '',
        protocolPorts,
        environmentVariables,
      });

      // 设置图标预览
      setIconPreview(record.iconUrl || '');
    } else {
      form.resetFields();
      // 新建时设置默认值
      form.setFieldsValue({
        protocolPorts: [{ protocol: 'http', port: '8080' }],
        environmentVariables: [],
        status: true,
      });

      // 清空图标预览
      setIconPreview('');
    }
  }, [record, type, form]);

  // 验证所有必填字段
  const validateAllTabs = async () => {
    try {
      const values = await form.validateFields();
      return values;
    } catch (errorInfo) {
      // 找到第一个有错误的字段所在的tab
      const errorFields = errorInfo.errorFields || [];
      if (errorFields.length > 0) {
        const firstErrorField = errorFields[0].name[0];

        // 根据字段名确定应该切换到哪个tab
        if (
          [
            'name',
            'version',
            'appType',
            'isOfficial',
            'description',
            'tags',
            'iconUrl',
          ].includes(firstErrorField)
        ) {
          setActiveTab('basic');
          toast.error('请完善基础信息中的必填项');
        } else if (
          [
            'containerImage',
            'startupCommand',
            'protocolPorts',
            'environmentVariables',
            'minCpuCores',
            'minMemoryGb',
            'minGpuCount',
            'systemDiskSize',
            'systemDiskPath',
          ].includes(firstErrorField)
        ) {
          setActiveTab('container');
          toast.error('请完善容器信息中的必填项');
        } else if (['readme'].includes(firstErrorField)) {
          setActiveTab('usage');
          toast.error('请完善使用说明中的必填项');
        }
      }
      throw errorInfo;
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await validateAllTabs();

      // 处理表单数据
      const formData = {
        ...values,
        tags:
          typeof values.tags === 'string'
            ? values.tags
            : values.tags?.join(',') || '',
        protocolPorts: values.protocolPorts || [],
        environmentVariables: values.environmentVariables || [],
        // 确保数字字段为正确的数字类型
        minCpuCores: Number(values.minCpuCores),
        minMemoryGb: Number(values.minMemoryGb),
        minGpuCount: values.minGpuCount ? Number(values.minGpuCount) : 0,
        systemDiskSize: Number(values.systemDiskSize),
      };

      // 直接调用接口
      if (type === 'create') {
        await appTemplateService.createAppTemplate(formData);
        toast.success('创建成功');
      } else if (type === 'edit') {
        await appTemplateService.updateAppTemplate({
          ...formData,
          id: record?.id,
        });
        toast.success('更新成功');
      } else if (type === 'copy') {
        await appTemplateService.copyAppTemplate(
          record?.id,
          formData.name,
          formData.version
        );
        toast.success('复制成功');
      }

      // 接口成功后关闭模态框并触发刷新
      handleCancel();
      onSuccess?.();
    } catch (error) {
      console.error('Form validation or submission failed:', error);
    } finally {
      setLoading(false);
    }
  };

  // 取消操作
  const handleCancel = () => {
    form.resetFields();
    setActiveTab('basic');
    onCancel();
  };

  // 自定义上传方法
  const customUpload = async ({ file, onSuccess, onError, onProgress }) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      // formData.append('ossId', null);

      setIconUploadLoading(true);
      const response = await http.post('/ihmp/api/app-template/s3/files', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress({ percent });
        },
      });

      // 调用成功回调
      onSuccess(response, file);
      console.log(response);

    } catch (error) {
      console.error('Upload error:', error);
      onError(error);
    } finally {
      setIconUploadLoading(false);
    }
  };

  // 基础信息Tab内容
  const renderBasicInfoTab = () => (
    <div className="space-y-4">
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="name"
            label="应用名称"
            rules={[
              { required: true, message: '请输入应用名称' },
              { min: 2, max: 50, message: '应用名称长度为2-50个字符' },
            ]}
          >
            <Input placeholder="请输入应用名称" disabled={isView} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="version"
            label="版本号"
            rules={[
              { required: true, message: '请输入版本号' },
              // {
              //   pattern: /^\d+\.\d+\.\d+$/,
              //   message: '版本号格式应为 x.y.z，如 1.0.0',
              // },
            ]}
          >
            <Input placeholder="如: 1.0.0" disabled={isView} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="appType"
            label="应用类型"
            rules={[{ required: true, message: '请选择应用类型' }]}
          >
            <Select placeholder="请选择应用类型" disabled={isView}>
              {Object.entries(APP_TYPE_LABELS).map(([key, label]) => (
                <Option key={key} value={parseInt(key)}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="isOfficial"
            label="是否官方应用"
            rules={[{ required: true, message: '请选择是否官方应用' }]}
          >
            <Select placeholder="请选择" disabled={isView}>
              <Option value={1}>官方应用</Option>
              <Option value={0}>社区应用</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="description"
        label="应用描述"
        rules={[
          { required: true, message: '请输入应用描述' },
          { min: 10, max: 500, message: '应用描述长度为10-500个字符' },
        ]}
      >
        <TextArea
          rows={4}
          placeholder="请详细描述应用的功能和特点..."
          disabled={isView}
          showCount
          maxLength={500}
        />
      </Form.Item>

      <Form.Item
        name="tags"
        label="标签"
        help="多个标签用逗号分隔，有助于用户搜索和分类"
      >
        <Input placeholder="如: 开发工具,IDE,在线编程" disabled={isView} />
      </Form.Item>

      <Form.Item
        name="iconUrl"
        label="应用图标"
        help="支持 JPG、PNG 格式，建议使用正方形图片，大小不超过 2MB"
      >

        <Upload
          name="file"
          customRequest={customUpload}
          listType="picture-card"
          maxCount={1}
          accept="image/*"
          disabled={isView}
          showUploadList={false}
          beforeUpload={file => {
            const isImage = file.type.startsWith('image/');
            if (!isImage) {
              message.error('只能上传图片文件！');
              return false;
            }
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
              message.error('图片大小不能超过 2MB！');
              return false;
            }
            return true;
          }}
          onChange={info => {
            if (info.file.status === 'done') {
              // 使用自定义上传后，响应数据直接在response中
              const url = info.file.response;
              const ossUrl = getOssUrl(url);
              if (url) {
                form.setFieldsValue({ iconUrl: ossUrl });
                setIconPreview(ossUrl);
                message.success('图标上传成功');
              }
            }
          }}
        >
          <Spin spinning={iconUploadLoading}>
            {iconPreview ? (
              <div className="relative w-full h-full group">
                <img
                  src={iconPreview}
                  alt="应用图标"
                  className="w-full h-full object-cover rounded"
                />
                {!isView && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center rounded">
                    <EditOutlined className="text-white text-lg" />
                    <span className="text-white text-sm ml-2">编辑图标</span>
                  </div>
                )}
              </div>
            ) : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图标</div>
              </div>
            )}
          </Spin>

        </Upload>
      </Form.Item>

      {!isView && (
        <Form.Item
          name="status"
          label="状态"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch
            checkedChildren="启用"
            unCheckedChildren="禁用"
            disabled={isView}
          />
        </Form.Item>
      )}
    </div>
  );

  // 容器信息Tab内容
  const renderContainerInfoTab = () => (
    <div className="space-y-6">
      {/* 容器基础信息 */}
      <Card title="容器基础信息" size="small">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="containerImage"
              label="容器镜像"
              rules={[
                { required: true, message: '请输入容器镜像' },
                {
                  pattern: /^[a-zA-Z0-9._/-]+:[a-zA-Z0-9._-]+$/,
                  message: '镜像格式应为 image:tag，如 nginx:latest',
                },
              ]}
            >
              <Input placeholder="如: nginx:latest" disabled={isView} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="startupCommand"
              label="启动命令"
              help="容器启动时执行的命令，可选"
            >
              <Input
                placeholder="如: /bin/bash -c 'npm start'"
                disabled={isView}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>

      {/* 协议端口配置 */}
      <Card title="协议端口配置" size="small">
        <Form.List name="protocolPorts" >
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }, index) => (
                <Row key={key} gutter={16} align="top">
                  <Col span={10}>
                    <Form.Item
                      {...restField}
                      name={[name, 'protocol']}
                      label={index === 0 ? '协议' : ''}
                      rules={[{ required: true, message: '请选择协议' }]}
                    >
                      <Select placeholder="选择协议" disabled={isView}>
                        <Option value="http">HTTP</Option>
                        <Option value="tcp">TCP</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item
                      {...restField}
                      name={[name, 'port']}
                      label={index === 0 ? '端口' : ''}
                      rules={[
                        { required: true, message: '请输入端口号' },
                        {
                          pattern:
                            /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
                          message: '端口号范围为1-65535',
                        },
                      ]}
                    >
                      <Input placeholder="如: 8080" disabled={isView} />
                    </Form.Item>
                  </Col>
                  <Col span={4}>
                    {!isView && (
                      <Form.Item
                        {...restField}
                        label={index === 0 ? '操作' : ''}
                      >
                        <Button
                          type="text"
                          danger
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}
                          disabled={fields.length === 1}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              ))}
              {!isView && (
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add({ protocol: 'http', port: '' })}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加协议端口
                  </Button>
                </Form.Item>
              )}
            </>
          )}
        </Form.List>
      </Card>

      {/* 环境变量配置 */}
      <EnvironmentVariablesInput
        isView={isView}
        title="环境变量配置"
        showTitle={true}
        fieldName="environmentVariables"
        placeholder={{
          key: '如: DATABASE_URL',
          value: '如: mysql://localhost:3306/db'
        }}
        size="small"
        helpText="💡 提示：环境变量将在应用启动时注入到容器中，请确保变量名和值的正确性。"
      />

      <Divider />

      {/* 资源要求 */}
      <Card title="资源要求" size="small">
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="minCpuCores"
              label="最少CPU核数"
              rules={[
                { required: true, message: '请输入最少CPU核数' },
                {
                  validator: (_, value) => {
                    const num = Number(value);
                    if (isNaN(num) || num < 1 || num > 64) {
                      return Promise.reject(new Error('CPU核数范围为1-64'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                type="number"
                min={1}
                max={64}
                placeholder="1"
                disabled={isView}
                addonAfter="核"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="minMemoryGb"
              label="最少内存"
              rules={[
                { required: true, message: '请输入最少内存' },
                {
                  validator: (_, value) => {
                    const num = Number(value);
                    if (isNaN(num) || num < 1 || num > 512) {
                      return Promise.reject(new Error('内存范围为1-512GB'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                type="number"
                min={1}
                max={512}
                placeholder="4"
                disabled={isView}
                addonAfter="GB"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="minGpuCount"
              label="最少GPU数量"
              rules={[
                {
                  validator: (_, value) => {
                    if (!value) return Promise.resolve(); // 可选字段
                    const num = Number(value);
                    if (isNaN(num) || num < 0 || num > 8) {
                      return Promise.reject(new Error('GPU数量范围为0-8'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                type="number"
                min={0}
                max={8}
                placeholder="0"
                disabled={isView}
                addonAfter="张"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="systemDiskSize"
              label="系统盘大小"
              rules={[
                { required: true, message: '请输入系统盘大小' },
                {
                  validator: (_, value) => {
                    const num = Number(value);
                    if (isNaN(num) || num < 20 || num > 1000) {
                      return Promise.reject(
                        new Error('系统盘大小范围为20-1000GB')
                      );
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Input
                type="number"
                min={20}
                max={1000}
                placeholder="80"
                disabled={isView}
                addonAfter="GB"
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="systemDiskPath"
              label="系统盘挂载路径"
              help="容器内的挂载路径，可选"
            >
              <Input placeholder="如: /home/<USER>" disabled={isView} />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    </div>
  );

  // 使用说明Tab内容
  const renderUsageInstructionsTab = () => (
    <div className="space-y-4">
      <Form.Item
        name="readme"
        label="使用说明"
        rules={
          [
            // { required: true, message: '请输入使用说明' },
            // { min: 50, message: '使用说明至少需要50个字符' },
          ]
        }
      >
        <TextArea
          rows={20}
          placeholder="请详细描述应用的使用方法、配置说明、注意事项等..."
          disabled={isView}
          showCount
        />
      </Form.Item>
    </div>
  );

  const tabItems = [
    {
      key: 'basic',
      label: '基础信息',
      children: renderBasicInfoTab(),
    },
    {
      key: 'container',
      label: '容器信息',
      children: renderContainerInfoTab(),
    },
    {
      key: 'usage',
      label: '使用说明',
      children: renderUsageInstructionsTab(),
    },
  ];

  return (
    <Modal
      title={title}
      open={visible}
      onCancel={handleCancel}
      width={900}
      footer={
        isView
          ? [
            <Button key="close" onClick={handleCancel}>
              关闭
            </Button>,
          ]
          : [
            <Button key="cancel" onClick={handleCancel}>
              取消
            </Button>,
            <Button
              key="submit"
              type="primary"
              loading={loading || iconUploadLoading}
              onClick={handleSubmit}
            >
              {type === 'create' ? '创建' : type === 'edit' ? '更新' : '复制'}
            </Button>,
          ]
      }
    >
      <Form form={form} layout="vertical" disabled={isView}>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          type="line"
        />
      </Form>
    </Modal>
  );
};

export default AppTemplateFormModal;
