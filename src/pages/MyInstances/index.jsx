import React, { useState, useEffect, useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Tabs,
  Pagination,
  Typography
} from 'antd';
import { formatDate } from '@/lib/utils';
import {
  getInstancesFromAPI,
} from '@/services/instanceService';
import { useRequest } from 'ahooks';
import {
  Server,
  Cpu,
  HardDrive,
  Zap,
  Play,
  Square,
  RotateCcw,
  Terminal,
  Monitor,
  Trash2,
  Clock,
  Activity,
  ExternalLink,
  AlertCircle,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import PageHeader from '@/components/PageHeader';
import SSHTerminalDialog from '@/components/SSHTerminalDialog';
import InstanceDetailsDialog from './components/InstanceDetailsDialog';

const { Text } = Typography

// 数据转换函数：将 API 数据转换为组件期望的格式
const convertAPIInstanceToInstance = apiInstance => {
  // 解析端口映射
  const parsePortsMapping = (portsMapping) => {
    try {
      if (typeof portsMapping === 'string') {
        return JSON.parse(portsMapping);
      }
      return portsMapping || [];
    } catch (error) {
      console.error('解析端口映射失败:', error);
      return [];
    }
  };

  // 解析元数据
  const parseMetaData = (metaData) => {
    try {
      if (typeof metaData === 'string') {
        return JSON.parse(metaData);
      }
      return metaData || {};
    } catch (error) {
      console.error('解析元数据失败:', error);
      return {};
    }
  };

  const portsMapping = parsePortsMapping(apiInstance.portsMapping);
  const metaData = parseMetaData(apiInstance.metaData);

  const [appName, appVersion] = (apiInstance.imageAddress.split('/').pop() || ':').split(':')

  return {
    id: apiInstance.id.toString(),
    name: apiInstance.name,
    deviceId: apiInstance.id.toString(),
    status: apiInstance.instanceStatus,
    cpu: `${apiInstance.cpuCores}核 ${apiInstance.cpuCode}`,
    memory: `${apiInstance.memorySize} GB`,
    gpu: apiInstance.gpuCode
      ? `${apiInstance.gpuNum || 1}×${apiInstance.gpuName}`
      : '无',
    storage: `${apiInstance.diskSize} GB`,
    startTime: apiInstance.createdAt,
    userId: apiInstance.userId || '1',
    applications: [], // 需要根据模板信息填充
    applicationInfo: apiInstance.imageAddress
      ? {
        name: appName,
        version: appVersion,
        icon: '🚀',
        ports: portsMapping.map(p => p.port),
        accessUrl: metaData.url ? (Array.isArray(metaData.url) ? metaData.url[0] : metaData.url) : undefined,
      }
      : undefined,
    sshInfo: apiInstance.sshAddress ? {
      host: apiInstance.sshAddress,
      port: 22,
      username: apiInstance.sshLoginUser || 'root',
    } : null,
    instanceUuid: apiInstance.instanceUuid,
    machineId: apiInstance.machineId,
    externalId: apiInstance.externalId,
    imageAddress: apiInstance.imageAddress,
    launchedAt: apiInstance.launchedAt,
    terminatedAt: apiInstance.terminatedAt,
    metaData: metaData,
    portsMapping: portsMapping,
  };
};

const MyInstances = () => {
  const [searchParams] = useSearchParams();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const sshTerminalRef = useRef();
  const instanceDetailsDialogRef = useRef();

  // 获取实例列表数据
  const {
    data: instancesData,
    loading: instancesLoading,
  } = useRequest(
    () => getInstancesFromAPI({}, currentPage, pageSize),
    {
      refreshDeps: [currentPage, pageSize],
    }
  );



  // 转换实例列表
  const instanceList = useMemo(() => {
    if (!instancesData?.records) return [];
    return [...instancesData.records]
      .map(convertAPIInstanceToInstance)
  }, [instancesData]);

  // 处理从其他页面跳转过来的实例ID参数
  useEffect(() => {
    const instanceId = searchParams.get('id');
    if (instanceId && instancesData?.records) {
      const apiInstance = instancesData.records.find(
        inst => inst.id.toString() === instanceId
      );
      if (apiInstance) {
        instanceDetailsDialogRef.current.open(
          convertAPIInstanceToInstance(apiInstance)
        );
      }
    }
  }, [searchParams, instancesData]);

  const handleSSHConnect = instance => {
    sshTerminalRef.current.open(instance)
  };

  return (
    <div className="min-h-screen space-y-6">
      <PageHeader title="我的实例" subtitle="管理您的计算实例" >
        <Text type="secondary">共 {instancesData?.total} 个实例</Text>
      </PageHeader>

      {/* 实例列表 */}
      {instancesLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-gray-500">加载中...</p>
        </div>
      ) : instanceList.length === 0 ? (
        <div className="text-center py-16">
          <Server className="w-20 h-20 text-gray-300 mx-auto mb-6" />
          <h3 className="text-xl font-medium text-gray-900 mb-3">
            暂无实例
          </h3>
          <p className="text-gray-500 mb-6">
            您还没有租用任何计算实例
          </p>
          <Button>前往算力市场</Button>
        </div>
      ) : (
        <>
          <div className="space-y-4">
            {instanceList.map(instance => (
              <InstanceCard
                key={instance.id}
                instance={instance}
                onSSHConnect={handleSSHConnect}
                onViewDetails={instance =>
                  instanceDetailsDialogRef.current.open(instance)
                }
              />
            ))}
          </div>

          {/* 分页 */}
          {instancesData && instancesData.total > pageSize && (
            <div className="mt-6 flex justify-center">
              <Pagination
                current={currentPage}
                total={instancesData.total}
                pageSize={pageSize}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                }
                onChange={(page, size) => {
                  setCurrentPage(page);
                  if (size !== pageSize) {
                    setPageSize(size);
                  }
                }}
              />
            </div>
          )}
        </>
      )}

      {/* 实例详情对话框 */}
      <InstanceDetailsDialog ref={instanceDetailsDialogRef} />

      <SSHTerminalDialog ref={sshTerminalRef} />
    </div>
  );
};

const InstanceCard = ({ instance, onAction, onSSHConnect, onViewDetails }) => {
  const getStatusIcon = status => {
    switch (status) {
      case 'CREATING':
      case 'RESTARTING':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      case 'RUNNING':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'STOPPED':
        return <XCircle className="w-4 h-4 text-gray-600" />;
      case 'FAILED':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusText = status => {
    switch (status) {
      case 'CREATING':
        return '创建中';
      case 'RUNNING':
        return '运行中';
      case 'STOPPED':
        return '已停止';
      case 'FAILED':
        return '失败';
      case 'RESTARTING':
        return '重启中';
      default:
        return '未知';
    }
  };

  const getStatusColor = status => {
    switch (status) {
      case 'CREATING':
      case 'RESTARTING':
        return 'text-yellow-600 bg-yellow-50';
      case 'RUNNING':
        return 'text-green-600 bg-green-50';
      case 'STOPPED':
        return 'text-gray-600 bg-gray-50';
      case 'FAILED':
        return 'text-red-600 bg-red-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <Card className="hover:shadow-lg transition-shadow border-none">
      <CardContent className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              <h3 className="text-lg font-semibold text-gray-900">
                {instance.name}
              </h3>
              <div
                className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                  instance.status
                )}`}
              >
                {getStatusIcon(instance.status)}
                {getStatusText(instance.status)}
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
              <div className="flex items-center gap-2">
                <Cpu className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">CPU</div>
                  <div className="text-sm font-medium">{instance.cpu}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <HardDrive className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">内存</div>
                  <div className="text-sm font-medium">{instance.memory}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Zap className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">GPU</div>
                  <div className="text-sm font-medium">{instance.gpu}</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Server className="w-4 h-4 text-gray-400" />
                <div>
                  <div className="text-xs text-gray-500">存储</div>
                  <div className="text-sm font-medium">{instance.storage}</div>
                </div>
              </div>
            </div>

            {/* 应用信息 */}
            {instance.applicationInfo && (
              <div className="mb-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center gap-3 mb-2">
                  <span className="text-2xl">
                    {instance.applicationInfo.icon}
                  </span>
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-blue-900">
                        {instance.applicationInfo.name}
                      </h4>
                      <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                        v{instance.applicationInfo.version}
                      </span>
                      <div
                        className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${instance.applicationInfo.status === 'RUNNING'
                          ? 'text-green-600 bg-green-100'
                          : instance.applicationInfo.status === 'installing'
                            ? 'text-yellow-600 bg-yellow-100'
                            : instance.applicationInfo.status === 'failed'
                              ? 'text-red-600 bg-red-100'
                              : 'text-gray-600 bg-gray-100'
                          }`}
                      >
                        {instance.applicationInfo.status === 'RUNNING' && (
                          <CheckCircle className="w-3 h-3" />
                        )}
                        {instance.applicationInfo.status === 'installing' && (
                          <Activity className="w-3 h-3" />
                        )}
                        {instance.applicationInfo.status === 'failed' && (
                          <XCircle className="w-3 h-3" />
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 mt-1 text-xs text-blue-700">
                      <span>
                        端口: {instance.applicationInfo.ports.join(', ')}
                      </span>
                    </div>
                  </div>
                  {instance.applicationInfo.accessUrl &&
                    instance.applicationInfo.status === 'RUNNING' && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          const url = instance.applicationInfo?.accessUrl;
                          if (url) {
                            // 如果URL不包含协议，添加https://
                            const fullUrl = url.startsWith('http') ? url : `https://${url}`;
                            window.open(fullUrl, '_blank');
                          }
                        }}
                        className="text-blue-600 border-blue-200 hover:bg-blue-50"
                      >
                        <ExternalLink className="w-4 h-4 mr-1" />
                        访问
                      </Button>
                    )}
                </div>
              </div>
            )}

            <div className="flex items-center gap-6 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                <span>创建时间: {formatDate(instance.startTime)}</span>
              </div>
              {instance.launchedAt && (
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>启动时间: {formatDate(instance.launchedAt)}</span>
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2 ml-4">
            {/* 详情按钮 */}
            <Button
              size="sm"
              variant="outline"
              onClick={() => onViewDetails(instance)}
              className="w-24"
            >
              <Monitor className="w-4 h-4 mr-1" />
              详情
            </Button>

            {/* 运行中状态的操作 */}
            {instance.status === 'RUNNING' && ( // RUNNING
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onSSHConnect(instance)}
                  className="w-24"
                >
                  <Terminal className="w-4 h-4 mr-1" />
                  SSH
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAction(instance.id, 'stop')}
                  className="w-24"
                >
                  <Square className="w-4 h-4 mr-1" />
                  停止
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAction(instance.id, 'restart')}
                  className="w-24"
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  重启
                </Button>
              </>
            )}

            {/* 已停止状态的操作 */}
            {instance.status === 'STOPPED' && ( // STOPPED
              <>
                <Button
                  size="sm"
                  onClick={() => onAction(instance.id, 'start')}
                  className="w-24"
                >
                  <Play className="w-4 h-4 mr-1" />
                  启动
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onAction(instance.id, 'delete')}
                  className="w-24"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  删除
                </Button>
              </>
            )}

            {/* 失败状态的操作 */}
            {instance.status === 'FAILED' && ( // FAILED
              <>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onAction(instance.id, 'restart')}
                  className="w-24"
                >
                  <RotateCcw className="w-4 h-4 mr-1" />
                  重启
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => onAction(instance.id, 'delete')}
                  className="w-24"
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  删除
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
export default MyInstances;
