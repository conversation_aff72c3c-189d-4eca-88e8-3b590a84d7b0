import React, { useState, useImperativeHandle, forwardRef } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Modal, Tabs } from 'antd';
import { formatDate } from '@/lib/utils';


const { TabPane } = Tabs;

const InstanceDetailsDialog = forwardRef((props, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [instance, setInstance] = useState(null);

  useImperativeHandle(ref, () => ({
    open: instance => {
      setInstance(instance);
      setIsOpen(true);
    },
  }));

  const onClose = () => {
    setIsOpen(false);
    setInstance(null);
  };

  if (!instance) return null;

  const applications = instance.applicationInfo
    ? [instance.applicationInfo]
    : [];

  return (
    <Modal
      title={<div className="text-2xl font-bold">{instance.name}</div>}
      open={isOpen}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
      styles={{ body: { maxHeight: '90vh', overflow: 'auto', padding: 24 } }}
    >
      <Tabs defaultActiveKey="overview" className="w-full">
        <TabPane tab="概览" key="overview">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>基本信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">实例ID</span>
                    <span className="font-medium">{instance.id}</span>
                  </div>
                  {instance.instanceUuid && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">实例UUID</span>
                      <span className="font-medium text-xs">
                        {instance.instanceUuid}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">状态</span>
                    <span className="font-medium">
                      {instance.status === 'RUNNING'
                        ? '运行中'
                        : instance.status === 'stopped'
                          ? '已停止'
                          : instance.status === 'pending'
                            ? '启动中'
                            : '错误'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">创建时间</span>
                    <span className="font-medium">
                      {formatDate(instance.startTime)}
                    </span>
                  </div>
                  {instance.launchedAt && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">启动时间</span>
                      <span className="font-medium">
                        {formatDate(instance.launchedAt)}
                      </span>
                    </div>
                  )}
                  {instance.imageAddress && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">镜像地址</span>
                      <span className="font-medium text-xs">
                        {instance.imageAddress}
                      </span>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>硬件配置</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">CPU</span>
                    <span className="font-medium">{instance.cpu}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">内存</span>
                    <span className="font-medium">{instance.memory}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">GPU</span>
                    <span className="font-medium">{instance.gpu}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">存储</span>
                    <span className="font-medium">{instance.storage}</span>
                  </div>
                  {instance.portsMapping &&
                    instance.portsMapping.length > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">端口映射</span>
                        <span className="font-medium">
                          {instance.portsMapping
                            .map(p => `${p.port}/${p.protocol}`)
                            .join(', ')}
                        </span>
                      </div>
                    )}
                </CardContent>
              </Card>
            </div>
          </div>
        </TabPane>

        <TabPane tab="监控（TODO）" key="monitoring">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>CPU使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">45%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: '45%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>内存使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">68%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: '68%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>GPU使用率</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">82%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: '82%' }}
                    ></div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabPane>

        {/* <TabPane tab="应用" key="applications">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {applications.length > 0 ? (
                applications.map(app => (
                  <Card key={app.id}>
                    <CardHeader>
                      <div className="flex items-center gap-3">
                        <div className="text-2xl">{app.icon}</div>
                        <div>
                          <CardTitle>{app.name}</CardTitle>
                          <CardDescription>{app.version}</CardDescription>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-600 mb-4">
                        {app.description}
                      </p>
                      <div className="flex gap-2">
                        {app.accessUrl && (
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              const url = app.accessUrl;
                              if (url) {
                                const fullUrl = url.startsWith('http')
                                  ? url
                                  : `https://${url}`;
                                window.open(fullUrl, '_blank');
                              }
                            }}
                          >
                            <ExternalLink className="w-4 h-4 mr-1" />
                            访问
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          <Settings className="w-4 h-4 mr-1" />
                          配置
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-2 text-center py-8">
                  <Download className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    暂无应用
                  </h3>
                  <p className="text-gray-500 mb-4">您还没有安装任何应用</p>
                  <Button>前往应用市场</Button>
                </div>
              )}
            </div>
          </div>
        </TabPane> */}
      </Tabs>
    </Modal>
  );
});

export default InstanceDetailsDialog;
