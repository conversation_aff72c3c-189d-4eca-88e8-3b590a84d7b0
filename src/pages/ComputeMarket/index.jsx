import React, { useState, useMemo, useEffect, useRef } from 'react';
import { useSearchParams, useLocation } from 'react-router-dom';
import { Row, Col } from 'antd';
import { useRequest } from 'ahooks';
import { getGpuList, getCpuList, getRegionList } from '@/services/enums';
import { getMarketList } from '@/services/market';
import { appTemplateService } from '@/services/appTemplate';
import PageHeader from '@/components/PageHeader';
import AIRecommendationDialog from '@/components/AIRecommendationDialog';
import InstanceOrderWizard from '@/components/InstanceOrderWizard';
import FilterPanel from './components/FilterPanel';
import ResourceList from './components/ResourceList';
import ApplicationSelector from './components/ApplicationSelector';
import ApplicationSelectionDialog from '../../components/ApplicationSelectionDialog';


const ComputeMarket = () => {
  const [searchParams] = useSearchParams();
  const location = useLocation();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [selectedApplication, setSelectedApplication] = useState('');
  const [selectedGpuModel, setSelectedGpuModel] = useState('all');
  const [selectedCpuModel, setSelectedCpuModel] = useState('all');
  const [showAIDialog, setShowAIDialog] = useState(false);
  const [showApplicationDialog, setShowApplicationDialog] = useState(false);

  const orderWizardRef = useRef();
  const handleRentDevice = device => {
    orderWizardRef.current.open(device, selectedApplication
      ? appTemplates.find(app => app.id === selectedApplication)
      : null);
  };

  // 分页参数
  const [pagination, setPagination] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  // 获取 GPU 列表
  const { data: gpuList = [], loading: gpuLoading } = useRequest(getGpuList, {
    onError: error => {
      console.error('获取 GPU 列表失败:', error);
    },
  });

  // 获取 CPU 列表
  const { data: cpuList = [], loading: cpuLoading } = useRequest(getCpuList, {
    onError: error => {
      console.error('获取 CPU 列表失败:', error);
    },
  });

  // 获取区域列表
  const { data: regionList = [], loading: regionLoading } = useRequest(
    getRegionList,
    {
      onError: error => {
        console.error('获取区域列表失败:', error);
      },
    }
  );

  // 获取应用模板数据
  const { data: appTemplates } = useRequest(async () => {
    const data = await appTemplateService.getAppTemplates({
      pageIndex: 1,
      pageSize: 100,
      status: 1, // 只获取启用的应用
    });
    return data.records || []
  })

  // 构建查询参数
  const buildQueryParams = useMemo(() => {
    const params = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize,
    };

    // 添加搜索条件
    if (searchTerm) {
      params.machineName = searchTerm;
    }

    // 添加设备类型
    if (selectedType !== 'all') {
      params.machineType = selectedType;
    }

    // 添加区域筛选
    if (selectedLocation !== 'all') {
      const region = regionList.find(r => r.regionName === selectedLocation);
      if (region) {
        params.regionCode = region.id;
      }
    }

    // 添加 GPU 型号筛选
    if (selectedGpuModel !== 'all') {
      const gpu = gpuList.find(g => g.gpuName === selectedGpuModel);
      if (gpu) {
        params.code = gpu.gpuCode;
        if (selectedType === 'GPU') {
          params.minGpuCode = gpu.gpuCode;
        }
      }
    }

    // 添加 CPU 型号筛选
    if (selectedCpuModel !== 'all') {
      const cpu = cpuList.find(c => c.cpuName === selectedCpuModel);
      if (cpu) {
        params.code = cpu.cpuCode;
      }
    }

    // 根据选择的应用添加硬件要求参数
    if (selectedApplication) {
      params.appId = selectedApplication;
    }

    return params;
  }, [
    pagination,
    searchTerm,
    selectedType,
    selectedLocation,
    selectedGpuModel,
    selectedCpuModel,
    selectedApplication,
    regionList,
    gpuList,
    cpuList,
  ]);

  // 获取算力市场列表
  const {
    data: marketData,
    loading: marketLoading,
  } = useRequest(() => getMarketList(buildQueryParams), {
    refreshDeps: [JSON.stringify(buildQueryParams)],
    retryCount: 3,
    retryInterval: 1000,
  });

  // 处理从应用市场跳转过来的应用参数
  useEffect(() => {
    const state = location.state;
    if (state?.selectedApplication) {
      setSelectedApplication(state.selectedApplication.id);
    }

    const appId = searchParams.get('app');
    if (appId) {
      setSelectedApplication(appId);
    }
  }, [searchParams, location.state]);

  // 直接使用接口返回的数据，不做转换
  const availableDevices = useMemo(() => {
    if (!marketData?.records) return [];
    return marketData.records;
  }, [marketData]);

  // 重置筛选条件
  const resetFilters = () => {
    setSearchTerm('');
    setSelectedType('all');
    setSelectedLocation('all');
    setSelectedApplication('');
    setSelectedGpuModel('all');
    setSelectedCpuModel('all');
  };




  return (
    <div className="min-h-screen">
      {/* 页面标题和AI推荐按钮 */}
      <div className="mb-6">

        <PageHeader
          title="算力市场 "
          subtitle="发现和租用最适合您需求的计算资源"
        />

      </div>

      {/* 主要内容区域：左侧筛选，右侧资源列表 */}
      <Row gutter={[24, 24]}>
        {/* 左侧筛选面板 */}
        <Col xs={24} lg={6}>
          <FilterPanel
            searchTerm={searchTerm}
            setSearchTerm={setSearchTerm}
            selectedType={selectedType}
            setSelectedType={setSelectedType}
            selectedLocation={selectedLocation}
            setSelectedLocation={setSelectedLocation}
            selectedGpuModel={selectedGpuModel}
            setSelectedGpuModel={setSelectedGpuModel}
            selectedCpuModel={selectedCpuModel}
            setSelectedCpuModel={setSelectedCpuModel}
            regionList={regionList}
            regionLoading={regionLoading}
            gpuList={gpuList}
            gpuLoading={gpuLoading}
            cpuList={cpuList}
            cpuLoading={cpuLoading}
            resetFilters={resetFilters}
          >
            <ApplicationSelector
              selectedApplication={selectedApplication}
              setSelectedApplication={setSelectedApplication}
              appTemplates={appTemplates}
              onShowApplicationDialog={() => setShowApplicationDialog(true)}
            />
          </FilterPanel>
        </Col>

        {/* 右侧资源列表 */}
        <Col xs={24} lg={18}>
          <ResourceList
            filteredDevices={availableDevices}
            marketLoading={marketLoading}
            pagination={pagination}
            setPagination={setPagination}
            marketData={marketData}
            handleRentDevice={handleRentDevice}
            onShowAIDialog={() => setShowAIDialog(true)}
            regionList={regionList}
          />
        </Col>
      </Row>

      {/* 应用选择对话框 */}
      <ApplicationSelectionDialog
        visible={showApplicationDialog}
        onClose={() => setShowApplicationDialog(false)}
        onSelect={(appId) => {
          setSelectedApplication(appId);
          setShowApplicationDialog(false);
        }}
      />

      {/* AI推荐对话框 */}
      <AIRecommendationDialog
        isOpen={showAIDialog}
        onClose={() => setShowAIDialog(false)}
        onRentDevice={handleRentDevice}
      />

      {/* 实例订购向导 */}
      <InstanceOrderWizard
        ref={orderWizardRef}
      />
    </div>
  );
};

export default ComputeMarket;
