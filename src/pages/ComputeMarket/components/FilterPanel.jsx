import React from 'react';
import { Card, Input, Button, Space, Typography, Spin, Segmented } from 'antd';
import { Filter, Cpu, Zap, RotateCcw, Layers } from 'lucide-react';

const { Title } = Typography;

const FilterPanel = ({
  searchTerm,
  setSearchTerm,
  selectedType,
  setSelectedType,
  selectedLocation,
  setSelectedLocation,
  selectedGpuModel,
  setSelectedGpuModel,
  selectedCpuModel,
  setSelectedCpuModel,
  regionList,
  regionLoading,
  gpuList,
  gpuLoading,
  cpuList,
  cpuLoading,
  resetFilters,
  children, // 应用选择区域
}) => {
  return (
    <Card
      className="sticky top-6 rounded-lg border-none shadow-sm transition-shadow hover:shadow-md"
      title={
        <Space>
          <Filter className="w-5 h-5" />
          筛选条件
        </Space>
      }
      extra={
        <Button
          type="text"
          size="small"
          icon={<RotateCcw className="w-3 h-3" />}
          onClick={resetFilters}
        >
          重置
        </Button>
      }
    >
      <Space direction="vertical" size="large" className="w-full">
        {/* 应用场景选择区域 - 通过children传入 */}
        {children}

        {/* 搜索框 */}
        <div>
          <Title level={5} className="!mb-3">
            搜索
          </Title>
          <Input.Search
            placeholder="搜索设备、CPU、GPU..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            allowClear
          />
        </div>

        {/* 设备类型筛选 */}
        <div>
          <Title level={5} className="!mb-3">
            设备类型
          </Title>
          <Segmented
            options={[
              {
                value: 'all',
                label: '全部',
                icon: <Layers className="!w-4 !h-4" />,
              },
              {
                value: 'GPU',
                label: 'GPU',
                icon: <Zap className="!w-4 !h-4" />,
              },
              {
                value: 'CPU',
                label: 'CPU',
                icon: <Cpu className="!w-4 !h-4" />,
              },
            ]}
            value={selectedType}
            onChange={value => {
              setSelectedType(value);
              // 切换设备类型时，重置相应的型号筛选
              if (value === 'CPU') {
                // 切换到 CPU 时，重置 GPU 筛选
                setSelectedGpuModel('all');
              } else if (value === 'GPU') {
                // 切换到 GPU 时，重置 CPU 筛选
                setSelectedCpuModel('all');
              }
              // 如果选择全部，保持当前筛选不变
            }}
            block
          />
        </div>

        {/* 地理位置筛选 */}
        <div>
          <Title level={5} className="!mb-3">
            地理位置
          </Title>
          <div className="flex flex-wrap gap-2">
            <Button
              type={selectedLocation === 'all' ? 'primary' : 'default'}
              size="small"
              onClick={() => setSelectedLocation('all')}
            >
              全部地区
            </Button>
            {regionLoading ? (
              <Spin size="small" />
            ) : (
              regionList.map(region => (
                <Button
                  key={region.id}
                  type={
                    selectedLocation === region.regionName
                      ? 'primary'
                      : 'default'
                  }
                  size="small"
                  onClick={() => setSelectedLocation(region.regionName)}
                >
                  {region.regionName}
                </Button>
              ))
            )}
          </div>
        </div>

        {/* GPU型号筛选 - 只在设备类型为 GPU 或全部时显示 */}
        {(selectedType === 'all' || selectedType === 'GPU') && (
          <div>
            <Title level={5} className="!mb-3">
              GPU型号
            </Title>
            <div className="flex flex-wrap gap-2">
              <Button
                type={selectedGpuModel === 'all' ? 'primary' : 'default'}
                size="small"
                onClick={() => {
                  setSelectedGpuModel('all');
                }}
              >
                全部型号
              </Button>
              {gpuLoading ? (
                <Spin size="small" />
              ) : (
                gpuList.map(gpu => (
                  <Button
                    key={gpu.id}
                    type={
                      selectedGpuModel === gpu.gpuName ? 'primary' : 'default'
                    }
                    size="small"
                    onClick={() => {
                      setSelectedGpuModel(gpu.gpuName);
                      // 选择了具体 GPU 型号，取消 CPU 选择
                      setSelectedCpuModel('all');
                    }}
                  >
                    {gpu.gpuName}
                  </Button>
                ))
              )}
            </div>
          </div>
        )}

        {/* CPU型号筛选 - 只在设备类型为 CPU 或全部时显示 */}
        {(selectedType === 'all' || selectedType === 'CPU') && (
          <div>
            <Title level={5} className="!mb-3">
              CPU型号
            </Title>
            <div className="flex flex-wrap gap-2">
              <Button
                type={selectedCpuModel === 'all' ? 'primary' : 'default'}
                size="small"
                onClick={() => {
                  setSelectedCpuModel('all');
                }}
              >
                全部型号
              </Button>
              {cpuLoading ? (
                <Spin size="small" />
              ) : (
                cpuList.map(cpu => (
                  <Button
                    key={cpu.id}
                    type={
                      selectedCpuModel === cpu.cpuName ? 'primary' : 'default'
                    }
                    size="small"
                    onClick={() => {
                      setSelectedCpuModel(cpu.cpuName);
                      // 选择了具体 CPU 型号，取消 GPU 选择
                      setSelectedGpuModel('all');
                    }}
                  >
                    {cpu.cpuName}
                  </Button>
                ))
              )}
            </div>
          </div>
        )}
      </Space>
    </Card>
  );
};

export default FilterPanel;
