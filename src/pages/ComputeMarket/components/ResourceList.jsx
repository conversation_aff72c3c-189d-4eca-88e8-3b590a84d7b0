import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Tag,
  Spin,
  Empty,
  Typography,
  Space,
  Row,
  Col,
  Pagination,
  Tooltip,
} from 'antd';
import {
  Server,
  MapPin,
  Cpu,
  HardDrive,
  Zap,
  ShoppingCart,
  Sparkles,
  DollarSign,
  Clock,
  Gauge,
} from 'lucide-react';
import { RainbowButton } from '@/components/magicui/rainbow-button';
import PriceInfo from '@/components/PriceInfo';

const { Title, Text } = Typography;

const ResourceList = ({
  filteredDevices,
  marketLoading,
  pagination,
  setPagination,
  marketData,
  handleRentDevice,
  onShowAIDialog,
  regionList = [], // 地理位置列表数据
}) => {
  // 根据regionCode获取地区名称
  const getRegionName = regionCode => {
    const region = regionList.find(
      r => r.id == regionCode || r.regionName === regionCode
    );
    return region ? region.regionName : regionCode;
  };

  const renderDeviceCard = device => (
    <Card
      key={device.id}
      className="w-full transition-all duration-300 hover:shadow-lg shadow-opacity-1 bg-white border border-gray-200/60 mb-4"
      styles={{
        body: { padding: '20px' },
      }}
    >
      {/* 顶部：设备名称、标签和价格 */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary rounded-lg shadow-sm">
            <Server className="w-5 h-5 text-white" />
          </div>
          <div>
            <div className="flex flex-row items-center">
              <Title level={5} className="!mb-0 text-gray-900 font-semibold">
                {device.machineName || device.machineAlias}
              </Title>
              <Tag
                color={device.machineType === 'GPU' ? 'blue' : 'green'}
                className="text-xs font-medium ml-2"
              >
                {device.machineType}
              </Tag>
            </div>
            <Text type="secondary" className="text-xs">
              {device.machineCode}
            </Text>
          </div>
        </div>

        {/* 右上角价格 */}
        <div className="text-right">
          <PriceInfo
            pricingList={device.resourcePricings || []}
            displayStyle="compact"
            placement="left"
            trigger="hover"
          />
        </div>
      </div>

      {/* 中间：硬件配置信息 */}
      <div className="bg-gray-50 rounded-lg p-4 mb-4">
        <div className="flex items-center gap-2 mb-3">
          <Gauge className="w-4 h-4 text-gray-600" />
          <Text className="text-sm font-medium text-gray-700">硬件配置</Text>
        </div>

        <Row gutter={[24, 16]}>
          {/* CPU */}
          <Col xs={12} sm={6}>
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-blue-100 rounded-lg">
                <Cpu className="w-4 h-4 text-blue-600" />
              </div>
              <div>
                <Text className="text-xs text-gray-500 block">CPU</Text>
                <Text className="text-sm font-semibold text-gray-900">
                  {device.cpuCode}
                </Text>
                <Text type="secondary" className="text-xs">
                  ({device.cpuNum}核)
                </Text>
              </div>
            </div>
          </Col>

          {/* 内存 */}
          <Col xs={12} sm={6}>
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-green-100 rounded-lg">
                <HardDrive className="w-4 h-4 text-green-600" />
              </div>
              <div>
                <Text className="text-xs text-gray-500 block">内存</Text>
                <Text className="text-sm font-semibold text-gray-900">
                  {device.memorySize}GB
                </Text>
              </div>
            </div>
          </Col>

          {/* GPU */}
          {device.gpuNum > 0 && (
            <Col xs={12} sm={6}>
              <div className="flex items-center gap-2">
                <div className="p-1.5 bg-orange-100 rounded-lg">
                  <Zap className="w-4 h-4 text-orange-600" />
                </div>
                <div>
                  <Text className="text-xs text-gray-500 block">GPU</Text>
                  <Text className="text-sm font-semibold text-gray-900">
                    {device.gpuName}
                  </Text>
                  <Text type="secondary" className="text-xs">
                    x{device.gpuNum}
                  </Text>
                </div>
              </div>
            </Col>
          )}

          {/* 存储 */}
          <Col xs={12} sm={6}>
            <div className="flex items-center gap-2">
              <div className="p-1.5 bg-purple-100 rounded-lg">
                <Server className="w-4 h-4 text-purple-600" />
              </div>
              <div>
                <Text className="text-xs text-gray-500 block">存储</Text>
                <Text className="text-sm font-semibold text-gray-900">
                  {device.diskSize}GB
                </Text>
                <Text type="secondary" className="text-xs">
                  {device.diskType}
                </Text>
              </div>
            </div>
          </Col>
        </Row>
      </div>

      {/* 底部：地理位置和操作按钮 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-1">
          <MapPin className="w-4 h-4 text-gray-400" />
          <Text type="secondary" className="text-sm">
            {getRegionName(device.regionCode)}
          </Text>
        </div>

        <Button
          type="primary"
          size="large"
          onClick={() => handleRentDevice(device)}
          icon={<ShoppingCart className="w-4 h-4" />}
          className="bg-primary h-9 text-sm px-6 border-0 rounded-sm shadow-md hover:shadow-lg transition-all duration-300"
        >
          立即租用
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* 顶部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Server className="w-6 h-6 text-blue-600" />
          <Title level={3} className="!mb-0 text-gray-800">
            可用算力资源
          </Title>
          <Text type="secondary">共 {marketData?.total || 0} 个资源</Text>
        </div>
        <RainbowButton
          className="rounded-lg bg-primary shadow-md hover:shadow-lg"
          onClick={onShowAIDialog}
        >
          <Sparkles className="w-4 h-4" />
          AI智能推荐
        </RainbowButton>
      </div>

      {/* 资源列表 */}
      {marketLoading ? (
        <div className="text-center py-16">
          <Spin size="large" />
          <div className="mt-4">
            <Title level={4} className="text-gray-600">
              正在加载算力资源...
            </Title>
            <Text type="secondary">请稍候</Text>
          </div>
        </div>
      ) : filteredDevices.length === 0 ? (
        <div className="text-center py-16">
          <Empty
            image={<Server className="w-20 h-20 text-gray-300 mx-auto mb-4" />}
            description={
              <div>
                <Title level={4} className="text-gray-500 mb-2">
                  暂无匹配的设备
                </Title>
                <Text type="secondary">请尝试调整筛选条件</Text>
              </div>
            }
          />
        </div>
      ) : (
        <>
          {/* 设备列表 - 每行一个 */}
          <div className="space-y-4">
            {filteredDevices.map(device => renderDeviceCard(device))}
          </div>

          {/* 分页 */}
          <div className="flex justify-end pb-4">
            <Pagination
              current={pagination.pageIndex}
              pageSize={pagination.pageSize}
              total={marketData?.total || 0}
              showSizeChanger
              showQuickJumper
              showTotal={(total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }
              onChange={(page, pageSize) => {
                setPagination({ pageIndex: page, pageSize });
              }}
              className=" p-4 rounded-lg "
            />
          </div>
        </>
      )}
    </div>
  );
};

export default ResourceList;
