import React, { useEffect, useState } from 'react';
import {
  Table,
  Card,
  Tag,
  Select,
  DatePicker,
  Space,
  Statistic,
  Row,
  Col,
  Button,
  Modal,
  Descriptions,
} from 'antd';
import {
  DollarSign,
  TrendingUp,
  Clock,
  Eye,
  Download,
  User,
  Server,
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { formatPrice, formatTime, formatDuration } from '@/utils/format';

const { Option } = Select;
const { RangePicker } = DatePicker;

const OrderManagement = () => {
  const { user } = useAuth();
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState(unde);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [orderDetailVisible, setOrderDetailVisible] = useState(false);

  useEffect(() => {
    fetchOrders();
  }, [statusFilter, user]);

  const fetchOrders = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // 模拟网络延时
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 模拟订单数据
      const mockOrders = [
        {
          id: 'order-1',
          userId: 'user-123',
          userName: '张三',
          deviceId: '1',
          deviceName: 'AI训练服务器-01',
          instanceId: 'inst-1',
          instanceName: 'DeepSeek训练实例',
          status: 'running',
          startTime: '2024-01-18T08:00:00Z',
          endTime: null,
          duration: 72, // 小时
          pricePerHour: 5.45,
          totalAmount: 392.4,
          createdAt: '2024-01-18T08:00:00Z',
        },
        {
          id: 'order-2',
          userId: 'user-456',
          userName: '李四',
          deviceId: '2',
          deviceName: '渲染节点-04',
          instanceId: 'inst-2',
          instanceName: 'Blender渲染实例',
          status: 'completed',
          startTime: '2024-01-15T10:00:00Z',
          endTime: '2024-01-17T18:00:00Z',
          duration: 56,
          pricePerHour: 6.0,
          totalAmount: 336.0,
          createdAt: '2024-01-15T10:00:00Z',
        },
        {
          id: 'order-3',
          userId: 'user-789',
          userName: '王五',
          deviceId: '1',
          deviceName: 'AI训练服务器-01',
          instanceId: 'inst-3',
          instanceName: 'PyTorch训练实例',
          status: 'running',
          startTime: '2024-01-20T14:00:00Z',
          endTime: null,
          duration: 18,
          pricePerHour: 5.45,
          totalAmount: 98.1,
          createdAt: '2024-01-20T14:00:00Z',
        },
        {
          id: 'order-4',
          userId: 'user-101',
          userName: '赵六',
          deviceId: '3',
          deviceName: '数据分析服务器',
          instanceId: 'inst-4',
          instanceName: 'Spark集群实例',
          status: 'paused',
          startTime: '2024-01-19T09:00:00Z',
          endTime: null,
          duration: 24,
          pricePerHour: 4.2,
          totalAmount: 100.8,
          createdAt: '2024-01-19T09:00:00Z',
        },
      ];

      setOrders(mockOrders);
    } catch (error) {
      console.error('获取订单数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 计算统计数据
  const stats = {
    totalRevenue: orders.reduce((sum, order) => sum + order.totalAmount, 0),
    todayRevenue: orders
      .filter(order => {
        const today = new Date().toDateString();
        const orderDate = new Date(order.startTime).toDateString();
        return orderDate === today && order.status === 'running';
      })
      .reduce((sum, order) => sum + order.pricePerHour * 8, 0), // 假设今天运行8小时
    expectedRevenue: orders
      .filter(order => order.status === 'running')
      .reduce((sum, order) => sum + order.pricePerHour * 24, 0), // 预期今日24小时收益
    activeOrders: orders.filter(order => order.status === 'running').length,
  };

  const handleViewOrderDetail = order => {
    setSelectedOrder(order);
    setOrderDetailVisible(true);
  };

  const columns = [
    {
      title: '订单ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: id => <span className="font-mono text-sm">{id}</span>,
    },
    {
      title: '用户信息',
      key: 'user',
      width: 150,
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.userName}</div>
          <div className="text-sm text-gray-500 font-mono">{record.userId}</div>
        </div>
      ),
    },
    {
      title: '设备/实例',
      key: 'device',
      render: (_, record) => (
        <div>
          <div className="font-medium">{record.deviceName}</div>
          <div className="text-sm text-gray-500">{record.instanceName}</div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: status => {
        const statusMap = {
          running: { color: 'green', text: '运行中' },
          completed: { color: 'blue', text: '已完成' },
          paused: { color: 'orange', text: '已暂停' },
          cancelled: { color: 'red', text: '已取消' },
        };
        const config = statusMap[status];
        return <Tag color={config.color}>{config.text}</Tag>;
      },
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 150,
      render: time => formatTime(time),
      sorter: (a, b) => new Date(a.startTime) - new Date(b.startTime),
    },
    {
      title: '使用时长',
      dataIndex: 'duration',
      key: 'duration',
      width: 100,
      render: duration => `${duration}小时`,
    },
    {
      title: '单价/小时',
      dataIndex: 'pricePerHour',
      key: 'pricePerHour',
      width: 100,
      render: price => formatPrice(price),
    },
    {
      title: '总金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: amount => (
        <span className="font-medium text-green-600">
          {formatPrice(amount)}
        </span>
      ),
      sorter: (a, b) => a.totalAmount - b.totalAmount,
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            size="small"
            icon={<Eye className="w-4 h-4" />}
            onClick={() => handleViewOrderDetail(record)}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  const filteredOrders = orders.filter(order => {
    if (!statusFilter) return true;
    return order.status === statusFilter;
  });

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">订单管理</h1>
          <p className="text-gray-600 mt-1">查看您托管设备的所有订单和收益</p>
        </div>
        <Button icon={<Download className="w-4 h-4" />}>导出订单</Button>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收益"
              value={stats.totalRevenue}
              prefix={<DollarSign className="w-4 h-4 text-green-500" />}
              valueStyle={{ color: '#52c41a' }}
              formatter={value => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">累计收益</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日收益"
              value={stats.todayRevenue}
              prefix={<TrendingUp className="w-4 h-4 text-primary" />}
              valueStyle={{ color: '#0d22ef' }}
              formatter={value => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">今日实际收益</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="预期收益"
              value={stats.expectedRevenue}
              prefix={<Clock className="w-4 h-4 text-orange-500" />}
              valueStyle={{ color: '#fa8c16' }}
              formatter={value => formatPrice(Number(value))}
            />
            <div className="mt-2 text-sm text-gray-500">今日预期收益</div>
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃订单"
              value={stats.activeOrders}
              prefix={<TrendingUp className="w-4 h-4 text-purple-500" />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div className="mt-2 text-sm text-gray-500">正在运行的订单</div>
          </Card>
        </Col>
      </Row>

      {/* 筛选器 */}
      <Card>
        <div className="flex flex-wrap gap-4 items-center">
          <Select
            placeholder="订单状态"
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
            allowClear
          >
            <Option value="running">运行中</Option>
            <Option value="completed">已完成</Option>
            <Option value="paused">已暂停</Option>
            <Option value="cancelled">已取消</Option>
          </Select>
          <RangePicker placeholder={['开始日期', '结束日期']} />
          <Button onClick={fetchOrders}>刷新</Button>
        </div>
      </Card>

      {/* 订单列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={filteredOrders}
          loading={loading}
          rowKey="id"
          pagination={{
            total: filteredOrders.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: total => `共 ${total} 条订单`,
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 订单详情模态框 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <Server className="w-5 h-5" />
            <span>订单详情</span>
          </div>
        }
        open={orderDetailVisible}
        onCancel={() => setOrderDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setOrderDetailVisible(false)}>
            关闭
          </Button>,
        ]}
        width={700}
      >
        {selectedOrder && (
          <div className="space-y-6">
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="订单ID" span={2}>
                <span className="font-mono">{selectedOrder.id}</span>
              </Descriptions.Item>
              <Descriptions.Item label="用户姓名">
                <div className="flex items-center space-x-2">
                  <User className="w-4 h-4" />
                  <span>{selectedOrder.userName}</span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="用户ID">
                <span className="font-mono text-sm">
                  {selectedOrder.userId}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="设备名称">
                <div className="flex items-center space-x-2">
                  <Server className="w-4 h-4" />
                  <span>{selectedOrder.deviceName}</span>
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="实例名称">
                {selectedOrder.instanceName}
              </Descriptions.Item>
              <Descriptions.Item label="订单状态">
                <Tag
                  color={
                    selectedOrder.status === 'running'
                      ? 'green'
                      : selectedOrder.status === 'completed'
                      ? 'blue'
                      : selectedOrder.status === 'paused'
                      ? 'orange'
                      : 'red'
                  }
                >
                  {selectedOrder.status === 'running'
                    ? '运行中'
                    : selectedOrder.status === 'completed'
                    ? '已完成'
                    : selectedOrder.status === 'paused'
                    ? '已暂停'
                    : '已取消'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {formatTime(selectedOrder.createdAt)}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="时间信息" bordered column={2}>
              <Descriptions.Item label="开始时间">
                {formatTime(selectedOrder.startTime)}
              </Descriptions.Item>
              <Descriptions.Item label="结束时间">
                {selectedOrder.endTime
                  ? formatTime(selectedOrder.endTime)
                  : '运行中'}
              </Descriptions.Item>
              <Descriptions.Item label="使用时长">
                {selectedOrder.duration} 小时
              </Descriptions.Item>
              <Descriptions.Item label="预计运行时间">
                {selectedOrder.status === 'running' ? '持续运行中' : '已结束'}
              </Descriptions.Item>
            </Descriptions>

            <Descriptions title="费用信息" bordered column={2}>
              <Descriptions.Item label="单价/小时">
                <span className="font-medium text-green-600">
                  {formatPrice(selectedOrder.pricePerHour)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="总金额">
                <span className="font-bold text-green-600 text-lg">
                  {formatPrice(selectedOrder.totalAmount)}
                </span>
              </Descriptions.Item>
              <Descriptions.Item label="预期今日收益" span={2}>
                {selectedOrder.status === 'running' ? (
                  <span className="font-medium text-blue-600">
                    {formatPrice(selectedOrder.pricePerHour * 24)}
                  </span>
                ) : (
                  <span className="text-gray-500">订单已结束</span>
                )}
              </Descriptions.Item>
            </Descriptions>

            {selectedOrder.status === 'running' && (
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">实时状态</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">当前状态</div>
                    <div className="font-medium text-green-600">正常运行</div>
                  </div>
                  <div>
                    <div className="text-gray-600">CPU使用率</div>
                    <div className="font-medium">
                      {Math.floor(Math.random() * 40 + 60)}%
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600">内存使用率</div>
                    <div className="font-medium">
                      {Math.floor(Math.random() * 30 + 50)}%
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OrderManagement;
