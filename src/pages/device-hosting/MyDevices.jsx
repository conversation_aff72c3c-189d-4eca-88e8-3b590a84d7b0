import { useState } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Tabs,
  Input,
  Select,
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  HistoryOutlined,
} from '@ant-design/icons';
import {
  RefreshCw,
  Server,
  Cpu,
  HardDrive,
  Zap,
  Terminal,
  CheckCircle,
  XCircle,
  Search,
} from 'lucide-react';
import { deviceService } from '@/services/machine';
import { useRequest } from 'ahooks';
import DeviceConnectionDialog from '@/components/DeviceConnectionDialog';
import { Popover } from 'antd';
import { toast } from 'sonner';

const { Option } = Select;

const MyDevices = () => {
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [devices, setDevices] = useState([]);
  const [total, setTotal] = useState(0);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [showConnectionDialog, setShowConnectionDialog] = useState(false);
  const [showAccessRecordsDialog, setShowAccessRecordsDialog] = useState(false);
  const [accessRecords, setAccessRecords] = useState([]);
  const [accessRecordsTotal, setAccessRecordsTotal] = useState(0);
  const [queryParams, setQueryParams] = useState({
    pageIndex: 1,
    pageSize: 10,
    machineName: '', // 设备名称搜索
    healthStatus: undefined, // 健康状态
    machineType: undefined, // 设备类型
    isPublic: undefined, // 是否上架
  });

  // 降级复制文本到剪贴板的方法（兼容旧浏览器）
  const fallbackCopyTextToClipboard = text => {
    const textArea = document.createElement('textarea');
    textArea.value = text;

    // 避免滚动到底部
    textArea.style.top = '0';
    textArea.style.left = '0';
    textArea.style.position = 'fixed';
    textArea.style.opacity = '0';

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
      const successful = document.execCommand('copy');
      if (successful) {
        toast.success('命令已复制到剪贴板');
      } else {
        toast.error('复制失败，请手动复制');
      }
    } catch (err) {
      toast.error('复制失败，请手动复制');
    }

    document.body.removeChild(textArea);
  };

  // 通用复制文本方法
  const copyToClipboard = (text, successMsg = '已复制到剪贴板') => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          toast.success(successMsg);
        })
        .catch(() => {
          fallbackCopyTextToClipboard(text);
        });
    } else {
      fallbackCopyTextToClipboard(text);
    }
  };

  // 使用 useRequest 管理设备列表加载
  const { loading, refresh } = useRequest(
    () => deviceService.getMachines(queryParams),
    {
      refreshDeps: [queryParams],
      onSuccess: data => {
        setDevices(data?.records || []);
        setTotal(data?.total || 0);
      },
      onError: () => {
        toast.error('加载设备列表失败');
      },
    }
  );

  const [accessRecordsParams, setAccessRecordsParams] = useState({
    pageIndex: 1,
    pageSize: 10,
  });

  // 使用 useRequest 管理接入记录加载
  const { loading: accessRecordsLoading, run: loadAccessRecords } = useRequest(
    () => deviceService.getAccessRecords(accessRecordsParams),
    {
      refreshDeps: [accessRecordsParams],
      // pollingInterval: 5000,
      onSuccess: data => {
        setAccessRecords(data?.records || []);
        setAccessRecordsTotal(data?.total || 0);
      },
      onError: () => {
        toast.error('加载接入记录失败');
      },
    }
  );

  // 处理查询参数变化
  const handleQueryChange = (key, value) => {
    setQueryParams(prev => ({
      ...prev,
      [key]: value,
      pageIndex: 1, // 重置到第一页
    }));
  };

  // 重置搜索条件
  const handleReset = () => {
    setQueryParams({
      pageIndex: 1,
      pageSize: 10,
      machineName: '',
      healthStatus: undefined,
      machineType: undefined,
      isPublic: undefined,
    });
  };

  // 处理分页变化
  const handleTableChange = pagination => {
    setQueryParams(prev => ({
      ...prev,
      pageIndex: pagination.current,
      pageSize: pagination.pageSize,
    }));
  };

  // 处理设备操作
  const handleDeviceAction = (deviceId, action) => {
    const device = devices.find(d => d.id === deviceId);
    if (!device) return;

    switch (action) {
      case 'start':
        toast.success(`${device.name || '设备'} 正在启动...`);
        break;
      case 'stop':
        toast.success(`${device.name || '设备'} 正在停止...`);
        break;
      case 'restart':
        toast.success(`${device.name || '设备'} 正在重启...`);
        break;
      case 'delete':
        toast.success(`${device.name || '设备'} 已删除`);
        refresh();
        break;
    }
  };

  const handleSSHConnect = device => {
    if (device.sshInfo) {
      const sshCommand = `ssh ${device.sshInfo.username}@${device.sshInfo.host} -p ${device.sshInfo.port}`;
      copyToClipboard(sshCommand, 'SSH连接命令已复制到剪贴板');
    }
  };

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">我的设备</h1>
        <p className="text-gray-600 mt-1">管理您的计算设备</p>
      </div>

      {/* 筛选器 */}
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
        <div className="p-6">
          <div className="flex justify-between items-center">
            <div className="flex gap-4 items-center">
              <Input
                placeholder="搜索设备名称..."
                prefix={<Search className="w-4 h-4 text-gray-400" />}
                value={queryParams.machineName}
                onChange={e => handleQueryChange('machineName', e.target.value)}
                style={{ width: 250 }}
                className="rounded-lg"
              />
              <Select
                placeholder="上架状态"
                value={queryParams.isPublic}
                onChange={value => handleQueryChange('isPublic', value)}
                style={{ width: 120 }}
                allowClear
                className="rounded-lg"
              >
                <Option value={1}>已上架</Option>
                <Option value={0}>未上架</Option>
              </Select>
              <Button onClick={handleReset} className="rounded-lg">
                重置
              </Button>
              <Button
                icon={<RefreshCw className="w-4 h-4" />}
                onClick={() => refresh()}
                className="rounded-lg"
              />
            </div>

            <div className="flex gap-3 items-center">
              <Button
                icon={<HistoryOutlined />}
                onClick={() => {
                  setShowAccessRecordsDialog(true);
                  loadAccessRecords();
                }}
              >
                接入记录
              </Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setShowConnectionDialog(true)}
              >
                接入新设备
              </Button>
            </div>
          </div>
        </div>

        {selectedRowKeys.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <Space>
              <span className="text-gray-600">
                已选择 {selectedRowKeys.length} 项
              </span>
              <Button size="small">批量启动</Button>
              <Button size="small">批量停止</Button>
              <Popconfirm
                title="确定要删除选中的设备吗？"
                onConfirm={() => {
                  /* 批量删除逻辑 */
                }}
                okText="确定"
                cancelText="取消"
              >
                <Button size="small" danger>
                  批量删除
                </Button>
              </Popconfirm>
            </Space>
          </div>
        )}
      </div>

      {/* 设备列表 */}
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm">
        <Table
          columns={[
            {
              title: '设备信息',
              dataIndex: 'machineName',
              key: 'machineName',
              minWidth: 160,
              render: (text, record) => (
                <div>
                  <div className="font-medium">{text}</div>
                  <div className="text-xs text-gray-500">
                    {record.machineCode}
                  </div>
                  <div className="text-xs text-gray-500">
                    {record.regions?.regionName}
                  </div>
                </div>
              ),
            },
            {
              title: '健康状态',
              dataIndex: 'healthStatus',
              key: 'healthStatus',
              minWidth: 100,
              render: healthStatus => {
                const statusConfig = {
                  0: {
                    text: '健康',
                    icon: <CheckCircle className="w-4 h-4" />,
                    bgColor: 'bg-green-50',
                    textColor: 'text-green-700',
                    borderColor: 'border-green-200',
                  },
                  1: {
                    text: '不健康',
                    icon: <XCircle className="w-4 h-4" />,
                    bgColor: 'bg-red-50',
                    textColor: 'text-red-700',
                    borderColor: 'border-red-200',
                  },
                };
                const config = statusConfig[healthStatus] || statusConfig[1];
                return (
                  <div
                    className={`inline-flex items-center gap-1 px-3 py-1 rounded-full border ${config.bgColor} ${config.textColor} ${config.borderColor}`}
                  >
                    {config.icon}
                    <span className="text-sm font-medium">{config.text}</span>
                  </div>
                );
              },
            },
            {
              title: '硬件配置',
              key: 'config',
              minWidth: 200,
              render: (_, record) => (
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm">
                    <Cpu className="w-4 h-4 text-gray-400" />
                    <span>
                      CPU: {record.cpuName} ({record.cpuNum}核)
                    </span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <HardDrive className="w-4 h-4 text-gray-400" />
                    <span>内存: {record.memorySize}GB</span>
                  </div>
                  {record.gpuNum > 0 && (
                    <div className="flex items-center gap-2 text-sm">
                      <Zap className="w-4 h-4 text-gray-400" />
                      <span>
                        GPU: {record.gpuName} x{record.gpuNum}
                      </span>
                    </div>
                  )}
                  <div className="flex items-center gap-2 text-sm">
                    <Server className="w-4 h-4 text-gray-400" />
                    <span>
                      存储: {record.diskSize}GB {record.diskType}
                    </span>
                  </div>
                  {/* 磁盘详细信息 */}
                  {record.diskStorageInfoVoList &&
                    record.diskStorageInfoVoList.length > 0 && (
                      <Popover
                        title="磁盘详细信息"
                        content={
                          <div className="max-w-md">
                            <Table
                              size="small"
                              pagination={false}
                              dataSource={record.diskStorageInfoVoList}
                              rowKey="device"
                              columns={[
                                {
                                  title: '设备',
                                  dataIndex: 'device',
                                  key: 'device',
                                  width: 100,
                                },
                                {
                                  title: '大小',
                                  dataIndex: 'size',
                                  key: 'size',
                                  width: 80,
                                  render: size => `${size}GB`,
                                },
                                {
                                  title: '类型',
                                  dataIndex: 'diskType',
                                  key: 'diskType',
                                  width: 60,
                                },
                                {
                                  title: '文件系统',
                                  dataIndex: 'fsType',
                                  key: 'fsType',
                                  width: 80,
                                },
                                {
                                  title: '挂载点',
                                  dataIndex: 'mountpoint',
                                  key: 'mountpoint',
                                  width: 100,
                                },
                                {
                                  title: '系统盘',
                                  dataIndex: 'isSystemDisk',
                                  key: 'isSystemDisk',
                                  width: 60,
                                  render: isSystemDisk => (
                                    <Tag
                                      color={isSystemDisk ? 'blue' : 'default'}
                                    >
                                      {isSystemDisk ? '是' : '否'}
                                    </Tag>
                                  ),
                                },
                              ]}
                            />
                          </div>
                        }
                        trigger="hover"
                        placement="right"
                      >
                        <div className="flex items-center gap-2 text-sm cursor-pointer text-blue-600 hover:text-blue-800">
                          <HardDrive className="w-4 h-4" />
                          <span>
                            磁盘详情 ({record.diskStorageInfoVoList.length}个)
                          </span>
                        </div>
                      </Popover>
                    )}
                </div>
              ),
            },
            {
              title: '价格信息',
              key: 'pricing',
              minWidth: 160,
              render: (_, record) => {
                const pricingList = record.resourcePricingList || [];

                // 构建价格表格数据
                const getPriceTableData = () => {
                  return pricingList.map(price => {
                    const hourlyPrice = price.pricePerUnit;
                    const dailyPrice = hourlyPrice * 24;
                    const monthlyPrice = dailyPrice * 30;
                    const yearlyPrice = dailyPrice * 365;

                    return {
                      key: `${price.resourceType}-${price.id}`,
                      resourceType:
                        price.resourceType === 'GPU' ? 'GPU' : '存储',
                      unit: (
                        <div className="flex flex-col items-center">
                          {price.pricingUnit}
                          {price.pricingUnit !== price.pricingUnitDesc &&
                            !!price.pricingUnitDesc && (
                              <span className="text-xs text-gray-500">
                                {price.pricingUnitDesc}
                              </span>
                            )}
                        </div>
                      ),
                      hourly: hourlyPrice.toFixed(2),
                      daily: dailyPrice.toFixed(2),
                      monthly: monthlyPrice.toFixed(2),
                      yearly: yearlyPrice.toFixed(2),
                    };
                  });
                };

                const priceTableColumns = [
                  {
                    title: '资源类型',
                    dataIndex: 'resourceType',
                    key: 'resourceType',
                    width: 70,
                  },
                  { title: '单位', dataIndex: 'unit', key: 'unit', width: 120 },
                  {
                    title: '小时',
                    dataIndex: 'hourly',
                    key: 'hourly',
                    width: 60,
                    render: val => `¥${val}`,
                  },
                  {
                    title: '日',
                    dataIndex: 'daily',
                    key: 'daily',
                    width: 60,
                    render: val => `¥${val}`,
                  },
                  {
                    title: '月',
                    dataIndex: 'monthly',
                    key: 'monthly',
                    width: 60,
                    render: val => `¥${val}`,
                  },
                  {
                    title: '年',
                    dataIndex: 'yearly',
                    key: 'yearly',
                    width: 60,
                    render: val => `¥${val}`,
                  },
                ];

                const priceTooltipContent = (
                  <div style={{ width: 500 }}>
                    <div className="mb-3 text-sm font-medium">价格详情</div>
                    <Table
                      columns={priceTableColumns}
                      dataSource={getPriceTableData()}
                      pagination={false}
                      size="small"
                      bordered
                      showHeader={true}
                    />
                    <div className="mt-2 text-xs text-gray-500">
                      * 月按30天计算，年按365天计算 • 实际计费以系统为准
                    </div>
                  </div>
                );

                // 获取最低GPU价格用于主要显示
                const gpuPrice = pricingList.find(
                  p => p.resourceType === 'GPU'
                );

                return (
                  <div className="space-y-1">
                    {gpuPrice ? (
                      <Popover
                        content={priceTooltipContent}
                        placement="left"
                        trigger="hover"
                      >
                        <div className="cursor-pointer hover:bg-gray-50 p-1 rounded">
                          <div className="text-sm font-medium text-orange-600">
                            ¥{gpuPrice.pricePerUnit} 起
                          </div>
                          <div className="text-xs text-gray-500">
                            {gpuPrice.pricingUnitDesc}
                          </div>
                          <div className="text-xs text-blue-500">
                            悬浮查看详情
                          </div>
                        </div>
                      </Popover>
                    ) : (
                      <div className="text-xs text-gray-400">暂无价格信息</div>
                    )}
                  </div>
                );
              },
            },
            {
              title: '上架状态',
              dataIndex: 'isPublic',
              key: 'isPublic',
              minWidth: 100,
              render: isPublic => (
                <Tag color={isPublic === 1 ? 'green' : 'default'}>
                  {isPublic === 1 ? '已上架' : '未上架'}
                </Tag>
              ),
            },
            {
              title: '操作',
              key: 'actions',
              minWidth: 160,
              render: (_, record) => (
                <Space>
                  <Tooltip title="SSH连接">
                    <Button
                      type="text"
                      icon={<Terminal className="w-4 h-4" />}
                      onClick={() => handleSSHConnect(record)}
                    />
                  </Tooltip>
                  <Tooltip title="查看详情">
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => setSelectedDevice(record)}
                    />
                  </Tooltip>
                  <Tooltip title="重启">
                    <Button
                      type="text"
                      icon={<ReloadOutlined />}
                      onClick={() => handleDeviceAction(record.id, 'restart')}
                    />
                  </Tooltip>
                  <Popconfirm
                    title="确定要删除这个设备吗？"
                    onConfirm={() => handleDeviceAction(record.id, 'delete')}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Tooltip title="删除">
                      <Button type="text" danger icon={<DeleteOutlined />} />
                    </Tooltip>
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={devices}
          rowKey="id"
          loading={loading}
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
          }}
          pagination={{
            current: queryParams.pageIndex,
            pageSize: queryParams.pageSize,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          onChange={handleTableChange}
          locale={{
            emptyText: (
              <div className="text-center py-12">
                <Server className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  暂无设备
                </h3>
                <p className="text-gray-500 mb-4">您还没有添加任何计算设备</p>
                <Button
                  type="primary"
                  onClick={() => setShowConnectionDialog(true)}
                >
                  添加设备
                </Button>
              </div>
            ),
          }}
        />
      </div>

      {/* 设备详情对话框 */}
      <DeviceDetailsDialog
        open={!!selectedDevice}
        device={selectedDevice}
        onClose={() => setSelectedDevice(null)}
      />

      {/* 设备接入对话框 */}
      <DeviceConnectionDialog
        isOpen={showConnectionDialog}
        onClose={() => setShowConnectionDialog(false)}
        onDeviceAdded={() => {
          refresh();
          setShowConnectionDialog(false);
        }}
      />

      {/* 接入记录对话框 */}
      <Modal
        title="设备接入记录"
        open={showAccessRecordsDialog}
        onCancel={() => setShowAccessRecordsDialog(false)}
        footer={null}
        width={1000}
      >
        <Table
          columns={[
            {
              title: '设备名称',
              dataIndex: 'machineName',
              key: 'machineName',
            },
            {
              title: '节点名称',
              dataIndex: 'nodeName',
              key: 'nodeName',
            },
            {
              title: '接入状态',
              dataIndex: 'machineVerifyStatus',
              key: 'machineVerifyStatus',
              render: status => {
                const statusConfig = {
                  PENDING_ACCESS: { color: 'processing', text: '等待接入' },
                  ACCESS_SUCCESS: { color: 'success', text: '接入成功' },
                  ACCESS_FAILED: { color: 'error', text: '接入失败' },
                  PENDING_REPORT_DEVICE_INFORMATION: {
                    color: 'warning',
                    text: '等待上报设备信息',
                  },
                };
                const config = statusConfig[status] || {
                  color: 'default',
                  text: status,
                };
                return <Tag color={config.color}>{config.text}</Tag>;
              },
            },
            {
              title: '生成时间',
              dataIndex: 'generatedTime',
              key: 'generatedTime',
            },
            {
              title: '过期时间',
              dataIndex: 'expiresAt',
              key: 'expiresAt',
            },
            {
              title: '错误信息',
              dataIndex: 'errorMsg',
              key: 'errorMsg',
              render: errorMsg =>
                errorMsg ? (
                  <Tooltip title={errorMsg}>
                    <Tag color="error">查看错误</Tag>
                  </Tooltip>
                ) : (
                  '-'
                ),
            },
            {
              title: '操作',
              key: 'actions',
              render: (_, record) => (
                <Space>
                  {record.command && (
                    <Button
                      size="small"
                      onClick={() => {
                        copyToClipboard(record.command, '命令已复制到剪贴板');
                      }}
                    >
                      复制命令
                    </Button>
                  )}
                </Space>
              ),
            },
          ]}
          dataSource={accessRecords}
          rowKey="id"
          loading={accessRecordsLoading}
          pagination={{
            current: accessRecordsParams.pageIndex,
            pageSize: accessRecordsParams.pageSize,
            total: accessRecordsTotal,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (current, size) => {
              console.log('Pagination changed:', current, size);
              setAccessRecordsParams({
                pageIndex: current,
                pageSize: size,
              });
            },
          }}
        />
      </Modal>
    </div>
  );
};

// 设备详情对话框组件
const DeviceDetailsDialog = ({ open, device, onClose }) => {
  if (!device) return null;

  return (
    <Modal
      title={
        <div className="flex items-center gap-3">
          <div className="w-3 h-3 rounded-full bg-gradient-to-r from-blue-500 to-purple-600"></div>
          <div>
            <div className="text-xl font-bold text-gray-900">
              {device.machineName || device.machineAlias || `设备-${device.id}`}
            </div>
            <div className="text-sm text-gray-500 mt-1">
              {device.machineCode} • {device.regions?.regionName}
            </div>
          </div>
        </div>
      }
      open={open}
      onCancel={onClose}
      footer={null}
      width={1000}
      style={{ top: 20 }}
      className="device-details-modal"
    >
      <Tabs defaultActiveKey="overview" className="w-full">
        <Tabs.TabPane tab="概览" key="overview">
          <div className="space-y-6">
            {/* 状态卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-3 h-3 rounded-full ${
                      device.healthStatus === 0 ? 'bg-green-500' : 'bg-red-500'
                    }`}
                  ></div>
                  <div>
                    <div className="text-sm text-gray-600">健康状态</div>
                    <div className="font-semibold text-gray-900">
                      {device.healthStatus === 0 ? '健康' : '不健康'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div
                    className={`w-3 h-3 rounded-full ${
                      device.isPublic === 1 ? 'bg-blue-500' : 'bg-gray-400'
                    }`}
                  ></div>
                  <div>
                    <div className="text-sm text-gray-600">上架状态</div>
                    <div className="font-semibold text-gray-900">
                      {device.isPublic === 1 ? '已上架' : '未上架'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                  <div>
                    <div className="text-sm text-gray-600">设备类型</div>
                    <div className="font-semibold text-gray-900">
                      {device.machineType || 'GPU'}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* 基本信息卡片 */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      基本信息
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">设备ID</span>
                      <span className="font-medium text-gray-900">
                        {device.id}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">设备名称</span>
                      <span className="font-medium text-gray-900">
                        {device.machineName || '未命名'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">设备别名</span>
                      <span className="font-medium text-gray-900">
                        {device.machineAlias || '无'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">内网IP</span>
                      <span className="font-medium text-gray-900 font-mono text-sm">
                        {device.innerIp || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">地区</span>
                      <span className="font-medium text-gray-900">
                        {device.regions?.regionName || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 text-sm">租用截止</span>
                      <span className="font-medium text-gray-900">
                        {device.rentDeadline || '未知'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 硬件配置卡片 */}
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      硬件配置
                    </h3>
                  </div>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">CPU型号</span>
                      <span className="font-medium text-gray-900">
                        {device.cpuName || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">CPU核数</span>
                      <span className="font-medium text-gray-900">
                        {device.cpuNum || '未知'}核
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">CPU架构</span>
                      <span className="font-medium text-gray-900">
                        {device.cpuArch || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">内存大小</span>
                      <span className="font-medium text-gray-900">
                        {device.memorySize || '未知'}GB
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">GPU型号</span>
                      <span className="font-medium text-gray-900">
                        {device.gpuName || '无'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">GPU数量</span>
                      <span className="font-medium text-gray-900">
                        {device.gpuNum || 0}张
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                      <span className="text-gray-600 text-sm">存储类型</span>
                      <span className="font-medium text-gray-900">
                        {device.diskType || '未知'}
                      </span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-gray-600 text-sm">存储大小</span>
                      <span className="font-medium text-gray-900">
                        {device.diskSize || '未知'}GB
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* GPU详细规格 */}
            {device.gpuSpec && (
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-6">
                    <div className="w-2 h-2 rounded-full bg-purple-500"></div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      GPU详细规格
                    </h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">GPU名称</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.gpuName}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">显存大小</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.vramGb}GB
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">
                          CUDA核心数
                        </span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.cudaCores?.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">显存类型</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.vramType}
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600 text-sm">显存带宽</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.vramBandwidthGbps} GB/s
                        </span>
                      </div>
                    </div>
                    <div className="space-y-4">
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">FP32性能</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.tflopsFp32} TFLOPS
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">FP64性能</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.tflopsFp64} TFLOPS
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">BF16性能</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.tflopsBf16} TFLOPS
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                        <span className="text-gray-600 text-sm">INT8性能</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.topsInt8} TOPS
                        </span>
                      </div>
                      <div className="flex justify-between items-center py-2">
                        <span className="text-gray-600 text-sm">功耗</span>
                        <span className="font-medium text-gray-900">
                          {device.gpuSpec.tdpWatts}W
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 价格信息 */}
            {device.resourcePricingList &&
              device.resourcePricingList.length > 0 && (
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-6">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        价格信息
                      </h3>
                    </div>

                    {/* 价格表格 */}
                    {(() => {
                      // 构建价格表格数据
                      const getPriceTableData = () => {
                        return device.resourcePricingList.map(price => {
                          const hourlyPrice = price.pricePerUnit;
                          const dailyPrice = hourlyPrice * 24;
                          const monthlyPrice = dailyPrice * 30;
                          const yearlyPrice = dailyPrice * 365;

                          return {
                            key: `${price.resourceType}-${price.id}`,
                            resourceType:
                              price.resourceType === 'GPU' ? 'GPU' : '存储',
                            unit: (
                              <div className="flex flex-col items-center">
                                {price.pricingUnit}
                                {price.pricingUnit !== price.pricingUnitDesc &&
                                  !!price.pricingUnitDesc && (
                                    <span className="text-xs text-gray-500">
                                      {price.pricingUnitDesc}
                                    </span>
                                  )}
                              </div>
                            ),
                            hourly: `¥${hourlyPrice.toFixed(2)}`,
                            daily: `¥${dailyPrice.toFixed(2)}`,
                            monthly: `¥${monthlyPrice.toFixed(2)}`,
                            yearly: `¥${yearlyPrice.toFixed(2)}`,
                            updatedAt: price.updatedAt?.split(' ')[0],
                          };
                        });
                      };

                      const priceTableColumns = [
                        {
                          title: '资源类型',
                          dataIndex: 'resourceType',
                          key: 'resourceType',
                          width: 80,
                          align: 'center',
                        },
                        {
                          title: '单位',
                          dataIndex: 'unit',
                          key: 'unit',
                          width: 150,
                        },
                        {
                          title: '小时',
                          dataIndex: 'hourly',
                          key: 'hourly',
                          width: 80,
                          align: 'right',
                        },
                        {
                          title: '日',
                          dataIndex: 'daily',
                          key: 'daily',
                          width: 80,
                          align: 'right',
                        },
                        {
                          title: '月(30天)',
                          dataIndex: 'monthly',
                          key: 'monthly',
                          width: 100,
                          align: 'right',
                        },
                        {
                          title: '年(365天)',
                          dataIndex: 'yearly',
                          key: 'yearly',
                          width: 100,
                          align: 'right',
                        },
                        {
                          title: '更新时间',
                          dataIndex: 'updatedAt',
                          key: 'updatedAt',
                          width: 100,
                          align: 'center',
                        },
                      ];

                      return (
                        <div>
                          <Table
                            columns={priceTableColumns}
                            dataSource={getPriceTableData()}
                            pagination={false}
                            size="middle"
                            bordered
                            showHeader={true}
                            className="price-detail-table"
                          />
                          <div className="mt-4 text-xs text-gray-500 bg-gray-50 p-3 rounded">
                            <div className="flex items-center gap-2">
                              <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                              <span>月按30天计算，年按365天计算</span>
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <div className="w-1 h-1 rounded-full bg-gray-400"></div>
                              <span>实际计费以系统为准</span>
                            </div>
                          </div>
                        </div>
                      );
                    })()}
                  </div>
                </div>
              )}

            {device.sshInfo && (
              <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 rounded-full bg-gray-500"></div>
                    <h3 className="text-lg font-semibold text-gray-900">
                      SSH连接信息
                    </h3>
                  </div>
                  <div className="bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm mb-4">
                    ssh {device.sshInfo.username}@{device.sshInfo.host} -p{' '}
                    {device.sshInfo.port}
                  </div>
                  <Button
                    type="primary"
                    className="bg-blue-500 hover:bg-blue-600 border-blue-500"
                    onClick={() => {
                      const command = `ssh ${device.sshInfo?.username}@${device.sshInfo?.host} -p ${device.sshInfo?.port}`;
                      copyToClipboard(command, 'SSH命令已复制到剪贴板');
                    }}
                  >
                    复制SSH命令
                  </Button>
                </div>
              </div>
            )}

            {/* 磁盘详细信息 */}
            {device.diskStorageInfoVoList &&
              device.diskStorageInfoVoList.length > 0 && (
                <div className="bg-white border border-gray-200 rounded-xl shadow-sm hover:shadow-md transition-shadow">
                  <div className="p-6">
                    <div className="flex items-center gap-2 mb-6">
                      <div className="w-2 h-2 rounded-full bg-orange-500"></div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        磁盘详细信息
                      </h3>
                    </div>
                    <div className="overflow-x-auto">
                      <Table
                        size="small"
                        pagination={false}
                        dataSource={device.diskStorageInfoVoList}
                        rowKey="device"
                        className="border border-gray-200 rounded-lg"
                        columns={[
                          {
                            title: '设备',
                            dataIndex: 'device',
                            key: 'device',
                            width: 120,
                            render: device => (
                              <span className="font-mono text-sm">
                                {device}
                              </span>
                            ),
                          },
                          {
                            title: '大小',
                            dataIndex: 'size',
                            key: 'size',
                            width: 100,
                            render: size => (
                              <span className="font-medium">{size}GB</span>
                            ),
                          },
                          {
                            title: '磁盘类型',
                            dataIndex: 'diskType',
                            key: 'diskType',
                            width: 80,
                            render: diskType => (
                              <Tag
                                color={diskType === 'SSD' ? 'blue' : 'default'}
                              >
                                {diskType}
                              </Tag>
                            ),
                          },
                          {
                            title: '文件系统',
                            dataIndex: 'fsType',
                            key: 'fsType',
                            width: 100,
                            render: fsType => (
                              <span className="font-mono text-sm">
                                {fsType}
                              </span>
                            ),
                          },
                          {
                            title: '挂载点',
                            dataIndex: 'mountpoint',
                            key: 'mountpoint',
                            width: 120,
                            render: mountpoint => (
                              <span className="font-mono text-sm">
                                {mountpoint}
                              </span>
                            ),
                          },
                          {
                            title: '系统盘',
                            dataIndex: 'isSystemDisk',
                            key: 'isSystemDisk',
                            width: 80,
                            align: 'center',
                            render: isSystemDisk => (
                              <Tag color={isSystemDisk ? 'green' : 'default'}>
                                {isSystemDisk ? '是' : '否'}
                              </Tag>
                            ),
                          },
                        ]}
                      />
                    </div>
                  </div>
                </div>
              )}
          </div>
        </Tabs.TabPane>

        <Tabs.TabPane tab="监控" key="monitoring">
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">CPU使用率</h3>
                  <div className="text-3xl font-bold text-blue-600">45%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{ width: '45%' }}
                    ></div>
                  </div>
                </div>
              </Card>

              <Card>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">内存使用率</h3>
                  <div className="text-3xl font-bold text-green-600">68%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-green-600 h-2 rounded-full"
                      style={{ width: '68%' }}
                    ></div>
                  </div>
                </div>
              </Card>

              <Card>
                <div className="p-4">
                  <h3 className="text-lg font-semibold mb-2">GPU使用率</h3>
                  <div className="text-3xl font-bold text-purple-600">82%</div>
                  <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div
                      className="bg-purple-600 h-2 rounded-full"
                      style={{ width: '82%' }}
                    ></div>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </Tabs.TabPane>

        <Tabs.TabPane tab="设置" key="settings">
          <div className="space-y-6">
            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2">设备设置</h3>
                <p className="text-gray-600 mb-4">管理您的设备配置</p>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">自动重启</h4>
                      <p className="text-sm text-gray-600">
                        设备异常时自动重启
                      </p>
                    </div>
                    <Switch />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">监控告警</h4>
                      <p className="text-sm text-gray-600">
                        资源使用率告警通知
                      </p>
                    </div>
                    <Switch />
                  </div>
                </div>
              </div>
            </Card>

            <Card>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-2 text-red-600">
                  危险操作
                </h3>
                <p className="text-gray-600 mb-4">以下操作不可逆，请谨慎操作</p>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">重置设备</h4>
                      <p className="text-sm text-gray-600">
                        清除所有数据并重新初始化
                      </p>
                    </div>
                    <Button danger size="small">
                      重置
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">删除设备</h4>
                      <p className="text-sm text-gray-600">
                        永久删除此设备及所有数据
                      </p>
                    </div>
                    <Button danger size="small">
                      删除
                    </Button>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </Tabs.TabPane>
      </Tabs>
    </Modal>
  );
};

export default MyDevices;
