import http from '@/utils/http';

const API_BASE = '/ihmp';

/**
 * 订购应用实例
 * @param {Object} deployData 部署数据
 * @param {string} deployData.appTemplateId 应用模板ID
 * @param {string} deployData.machineId 机器ID
 * @param {string} deployData.instanceName 实例名称
 * @param {number} deployData.gpuRequest GPU卡数
 * @param {number} deployData.cpuRequest CPU核数
 * @param {number} deployData.memoryRequest 内存大小 单位G
 * @param {number} deployData.storageRequest 额外存储的数量
 * @param {Object} deployData.envVars 环境变量 {"key":"value"}
 * @param {string} deployData.priceModel 价格模式 ON_DEMAND/PACKAGE_MONTH/PACKAGE_YEAR
 * @param {boolean} deployData.isAutoRenewal 是否自动续费
 * @param {number} deployData.duration 购买持续时间，按需购买默认-1
 */
export function postInstanceOrder (data) {
  return http.post(`${API_BASE}/market/purchase`, data);
}