import http from '@/utils/http';

const API_BASE = '/ihmp';

/**
 * 部署服务
 */
export const deployService = {
  /**
   * 部署应用
   * @param {Object} deployData 部署数据
   * @param {string} deployData.appTemplateId 应用模板ID
   * @param {string} deployData.machineId 机器ID
   * @param {string} deployData.instanceName 实例名称
   * @param {number} deployData.gpuRequest GPU卡数
   * @param {number} deployData.cpuRequest CPU核数
   * @param {number} deployData.memoryRequest 内存大小 单位G
   * @param {number} deployData.storageRequest 额外存储的数量
   * @param {Object} deployData.envVars 环境变量 {"key":"value"}
   * @param {string} deployData.priceModel 价格模式 ON_DEMAND/PACKAGE_MONTH/PACKAGE_YEAR
   * @param {boolean} deployData.isAutoRenewal 是否自动续费
   * @param {number} deployData.duration 购买持续时间，按需购买默认-1
   */
  deployApp: (deployData) => {
    return http.post(`${API_BASE}/app/deploy`, deployData);
  },

  /**
   * 获取部署状态
   * @param {string} deploymentId 部署ID
   */
  getDeploymentStatus: (deploymentId) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/status`);
  },

  /**
   * 取消部署
   * @param {string} deploymentId 部署ID
   */
  cancelDeployment: (deploymentId) => {
    return http.post(`${API_BASE}/app/deploy/${deploymentId}/cancel`);
  },

  /**
   * 获取部署日志
   * @param {string} deploymentId 部署ID
   */
  getDeploymentLogs: (deploymentId) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/logs`);
  },

  /**
   * 重新部署
   * @param {string} deploymentId 部署ID
   */
  redeployApp: (deploymentId) => {
    return http.post(`${API_BASE}/app/deploy/${deploymentId}/redeploy`);
  },

  /**
   * 停止应用
   * @param {string} deploymentId 部署ID
   */
  stopApp: (deploymentId) => {
    return http.post(`${API_BASE}/app/deploy/${deploymentId}/stop`);
  },

  /**
   * 启动应用
   * @param {string} deploymentId 部署ID
   */
  startApp: (deploymentId) => {
    return http.post(`${API_BASE}/app/deploy/${deploymentId}/start`);
  },

  /**
   * 删除部署
   * @param {string} deploymentId 部署ID
   */
  deleteDeployment: (deploymentId) => {
    return http.delete(`${API_BASE}/app/deploy/${deploymentId}`);
  },

  /**
   * 获取应用访问地址
   * @param {string} deploymentId 部署ID
   */
  getAppAccessUrl: (deploymentId) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/access-url`);
  },

  /**
   * 更新应用配置
   * @param {string} deploymentId 部署ID
   * @param {Object} config 配置数据
   */
  updateAppConfig: (deploymentId, config) => {
    return http.put(`${API_BASE}/app/deploy/${deploymentId}/config`, config);
  },

  /**
   * 获取应用配置
   * @param {string} deploymentId 部署ID
   */
  getAppConfig: (deploymentId) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/config`);
  },

  /**
   * 扩缩容应用
   * @param {string} deploymentId 部署ID
   * @param {Object} scaleData 扩缩容数据
   * @param {number} scaleData.cpuRequest CPU核数
   * @param {number} scaleData.memoryRequest 内存大小
   * @param {number} scaleData.gpuRequest GPU卡数
   */
  scaleApp: (deploymentId, scaleData) => {
    return http.post(`${API_BASE}/app/deploy/${deploymentId}/scale`, scaleData);
  },

  /**
   * 获取应用监控数据
   * @param {string} deploymentId 部署ID
   * @param {Object} params 查询参数
   * @param {string} params.startTime 开始时间
   * @param {string} params.endTime 结束时间
   * @param {string} params.metric 监控指标 (cpu/memory/gpu/network)
   */
  getAppMetrics: (deploymentId, params) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/metrics`, { params });
  },

  /**
   * 获取应用事件日志
   * @param {string} deploymentId 部署ID
   * @param {Object} params 查询参数
   * @param {number} params.pageIndex 页码
   * @param {number} params.pageSize 每页大小
   * @param {string} params.level 日志级别 (info/warn/error)
   */
  getAppEvents: (deploymentId, params) => {
    return http.get(`${API_BASE}/app/deploy/${deploymentId}/events`, { params });
  }
};

/**
 * 部署状态枚举
 */
export const DEPLOYMENT_STATUS = {
  PENDING: 'PENDING',           // 等待中
  DEPLOYING: 'DEPLOYING',       // 部署中
  RUNNING: 'RUNNING',           // 运行中
  STOPPED: 'STOPPED',           // 已停止
  FAILED: 'FAILED',             // 部署失败
  CANCELLED: 'CANCELLED',       // 已取消
  SCALING: 'SCALING',           // 扩缩容中
  UPDATING: 'UPDATING',         // 更新中
};

/**
 * 部署状态标签映射
 */
export const DEPLOYMENT_STATUS_LABELS = {
  [DEPLOYMENT_STATUS.PENDING]: '等待中',
  [DEPLOYMENT_STATUS.DEPLOYING]: '部署中',
  [DEPLOYMENT_STATUS.RUNNING]: '运行中',
  [DEPLOYMENT_STATUS.STOPPED]: '已停止',
  [DEPLOYMENT_STATUS.FAILED]: '部署失败',
  [DEPLOYMENT_STATUS.CANCELLED]: '已取消',
  [DEPLOYMENT_STATUS.SCALING]: '扩缩容中',
  [DEPLOYMENT_STATUS.UPDATING]: '更新中',
};

/**
 * 部署状态颜色映射
 */
export const DEPLOYMENT_STATUS_COLORS = {
  [DEPLOYMENT_STATUS.PENDING]: 'default',
  [DEPLOYMENT_STATUS.DEPLOYING]: 'processing',
  [DEPLOYMENT_STATUS.RUNNING]: 'success',
  [DEPLOYMENT_STATUS.STOPPED]: 'default',
  [DEPLOYMENT_STATUS.FAILED]: 'error',
  [DEPLOYMENT_STATUS.CANCELLED]: 'default',
  [DEPLOYMENT_STATUS.SCALING]: 'processing',
  [DEPLOYMENT_STATUS.UPDATING]: 'processing',
};
